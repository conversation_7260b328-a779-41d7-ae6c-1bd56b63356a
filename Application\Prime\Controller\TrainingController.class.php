<?php
namespace Prime\Controller;

use Common\Controller\PrimeController;
use Util\AliSms;
use Util\HwSms;
/**
 * 培训管理
 * Class TrainingController
 * @package Prime\Controller
 */
class TrainingController extends PrimeController
{

    public function _initialize()
    {
        parent::_initialize();
    }

    /**
     * 培训订单列表
     */
    public function index() {
        $c_kw = [
            'id' => '订单ID',
            'user_name' => '学员姓名',
            'user_mobile' => '学员手机号',
            'station_id' => '服务站ID',
            'zsb_id' => '招就办ID',
        ];
        $where = [];
        $s_kw = I("get.kw");
        $s_val = I("get.val");
        $s_main_status = I("get.main_status");
        $s_sub_status = I("get.sub_status");

        // 调试：记录所有GET参数
        dolog('training/debug/get_params', "所有GET参数: " . json_encode($_GET));
        dolog('training/debug/search_params', "搜索参数: kw=$s_kw, val=$s_val, main_status=$s_main_status, sub_status=$s_sub_status");
        // 构建查询条件（使用新的二级状态）
        if ($s_main_status != '') $where['main_status'] = $s_main_status;
        if ($s_sub_status != '') $where['sub_status'] = $s_sub_status;

        // 关键词搜索
        $searchNoResults = false; // 标志：搜索是否无结果
        dolog('training/debug/search_condition', "搜索条件检查: s_kw='$s_kw', s_val='$s_val', 条件结果: " . (($s_kw && $s_val != '') ? 'true' : 'false'));

        if ($s_kw && $s_val != '') {
            // 记录搜索参数日志
            dolog('training/search', "进入搜索逻辑: kw=$s_kw, val=$s_val");

            switch ($s_kw) {
                case 'id':
                    $where['id'] = $s_val;
                    dolog('training/search/id', "按ID搜索: $s_val");
                    break;
                case 'user_name':
                    // 根据数据结构：通过UserJob表的name字段搜索，然后通过user_job_id关联TrainingOrder
                    dolog('training/search/user_name_start', "开始搜索学员姓名: $s_val");

                    // 在UserJob表中搜索name字段
                    $userJobModel = D('UserJob');
                    $userJobIds = $userJobModel->where(['name' => ['like', "%$s_val%"]])->getField('id', true);

                    dolog('training/search/userjob_name', "UserJob表name字段搜索结果: " . json_encode($userJobIds));

                    if ($userJobIds) {
                        // 通过user_job_id在TrainingOrder表中查找订单
                        $orderIds = D('TrainingOrder')->where(['user_job_id' => ['in', $userJobIds]])->getField('id', true);

                        if ($orderIds) {
                            $where['id'] = ['in', $orderIds];
                            dolog('training/search/user_name', "搜索姓名: $s_val, 找到UserJob ID: " . json_encode($userJobIds) . ", 对应订单ID: " . json_encode($orderIds));
                        } else {
                            $searchNoResults = true;
                            dolog('training/search/user_name', "搜索姓名: $s_val, 找到UserJob但没有对应的订单");
                        }
                    } else {
                        $searchNoResults = true;
                        dolog('training/search/user_name', "搜索姓名: $s_val, 在UserJob表中没有找到匹配记录");
                    }
                    break;
                case 'user_mobile':
                    // 根据数据结构：通过UserJob表的phone字段搜索，然后通过user_job_id关联TrainingOrder
                    dolog('training/search/user_mobile_start', "开始搜索学员手机号: $s_val");

                    // 在UserJob表中搜索phone字段
                    $userJobModel = D('UserJob');
                    $userJobIds = $userJobModel->where(['phone' => ['like', "%$s_val%"]])->getField('id', true);

                    dolog('training/search/userjob_phone', "UserJob表phone字段搜索结果: " . json_encode($userJobIds));

                    if ($userJobIds) {
                        // 通过user_job_id在TrainingOrder表中查找订单
                        $orderIds = D('TrainingOrder')->where(['user_job_id' => ['in', $userJobIds]])->getField('id', true);

                        if ($orderIds) {
                            $where['id'] = ['in', $orderIds];
                            dolog('training/search/user_mobile', "搜索手机号: $s_val, 找到UserJob ID: " . json_encode($userJobIds) . ", 对应订单ID: " . json_encode($orderIds));
                        } else {
                            $searchNoResults = true;
                            dolog('training/search/user_mobile', "搜索手机号: $s_val, 找到UserJob但没有对应的订单");
                        }
                    } else {
                        $searchNoResults = true;
                        dolog('training/search/user_mobile', "搜索手机号: $s_val, 在UserJob表中没有找到匹配记录");
                    }
                    break;
                case 'station_id':
                    // 根据数据结构：直接搜索TrainingOrder表的station_id字段
                    $where['station_id'] = $s_val;
                    dolog('training/search/station_id', "搜索服务站ID: $s_val");
                    break;
                case 'zsb_id':
                    // 根据数据结构：搜索TrainingOrder表的parent_station_id字段
                    $where['parent_station_id'] = $s_val;
                    dolog('training/search/zsb_id', "搜索招就办ID: $s_val (使用parent_station_id字段)");
                    break;
            }
        }

        // 检查是否有任何搜索/筛选条件
        $hasAnyFilter = false;
        if (($s_kw && $s_val != '') || $s_main_status != '' || $s_sub_status != '') {
            $hasAnyFilter = true;
        }

        // 调试：记录筛选条件检查结果
        dolog('training/debug/filter_check', "筛选条件检查: hasAnyFilter=" . ($hasAnyFilter ? 'true' : 'false'));
        dolog('training/debug/where_before_hide', "隐藏逻辑前的where条件: " . json_encode($where));

        // 只有在没有任何搜索/筛选条件时，才自动隐藏特定订单
        if (!$hasAnyFilter) {
            // 隐藏服务站id为4的订单和招就办id为32的订单
            $where['station_id'] = ['neq', 4];
            $where['zsb_id'] = ['neq', 32];
            dolog('training/debug/auto_hide', "应用自动隐藏条件");
        } else {
            dolog('training/debug/no_hide', "有搜索条件，不应用自动隐藏");
        }

        // 检查搜索是否无结果
        if ($searchNoResults) {
            // 如果搜索无结果，直接返回空列表
            dolog('training/search/no_results', "搜索无结果，返回空列表");
            $list = [];
            $count = 0;
            $page = $this->page($count, 20);
        } else {
            // 记录最终的查询条件
            dolog('training/search/final_where', "最终查询条件: " . json_encode($where));

            // 分页
            $obj = D("TrainingOrder");
            $count = $obj->where($where)->count();
            $page = $this->page($count, 20);

            // 获取订单列表
            $list = $obj->getOrderList($where, 'create_time desc', $page->firstRow, $page->listRows);

            // 注释：改为依赖表数据，移除实时计算逻辑
            // 招就办订单的收益信息直接使用数据库存储的字段：
            // station_profit, zsb_commission, platform_fee 等

            // 为招就办订单设置前端需要的详细收益字段，并添加支付记录信息
            foreach ($list as &$item) {
                if (!empty($item['is_zsb_order']) && $item['is_zsb_order']) {
                    // 设置详细收益信息（直接使用存储的字段）
                    $item['station_profit_detail'] = !empty($item['station_profit']) ? $item['station_profit'] : 0;
                    $item['zsb_commission_detail'] = !empty($item['zsb_commission']) ? $item['zsb_commission'] : 0;
                }

                // 查询支付记录，计算已付金额
                $paidAmount = D("PaymentRecord")
                    ->where(['order_id' => $item['id']])
                    ->sum('pay_amount');

                $item['paid_amount'] = $paidAmount ? $paidAmount : 0; // 已付金额（分）
                $item['remaining_amount'] = $item['fee_amount'] - $item['paid_amount']; // 待付金额（分）

                // 记录支付信息日志
                dolog('training/payment_info', "订单ID: {$item['id']}, 总金额: {$item['fee_amount']}, 已付: {$item['paid_amount']}, 待付: {$item['remaining_amount']}");
            }
            unset($item); // 清除引用
        }

        // 记录查询结果数量
        dolog('training/search/result_count', "查询结果数量: $count");



        // 获取项目和岗位列表，用于筛选
        $projectList = D("Project")->where(['status' => 1])->getField('id,name', true);
        $postList = D("ProjectPost")->where(['status' => 1])->select();
        $postMap = [];
        foreach ($postList as $post) {
            if (isset($projectList[$post['project_id']])) {
                $postMap[$post['id']] = $post['name'] . ' (' . $projectList[$post['project_id']] . ')';
            }
        }

        // 模板赋值（使用新的二级状态系统）
        $this->assign('main_status_list', $obj->main_status);
        $this->assign('sub_status_list', $obj->sub_status);
        $this->assign('project_list', $projectList);
        $this->assign('post_list', $postMap);

        $this->assign('_get', I('get.'));
        $this->assign('list', $list);
        $this->assign("page", $page->show());
        $this->assign('c_kw', $c_kw);

        // 添加管理员权限检查，用于统计报表显示
        $adminId = session('admin_id');
        $adminRoleId = session('admin_role_id');

        // 判断是否为管理员：用户ID为1或角色ID包含1
        $isAdmin = false;
        if ($adminId == 1) {
            $isAdmin = true;
        } elseif ($adminRoleId) {
            $roleIds = explode(',', $adminRoleId);
            $isAdmin = in_array('1', $roleIds);
        }

        // 调试信息
        \Think\Log::write('培训管理权限检查 - admin_id: ' . $adminId . ', admin_role_id: ' . $adminRoleId . ', is_admin: ' . ($isAdmin ? 'true' : 'false'), 'INFO');

        $this->assign('is_admin', $isAdmin);

        $this->display();
    }

    /**
     * 调试方法：显示当前用户权限信息
     */
    public function debugAuth()
    {
        $adminId = session('admin_id');
        $adminName = session('admin_name');
        $adminRole = session('admin_role');
        $adminRoleId = session('admin_role_id');

        $isAdmin = false;
        if ($adminId == 1) {
            $isAdmin = true;
        } elseif ($adminRoleId) {
            $roleIds = explode(',', $adminRoleId);
            $isAdmin = in_array('1', $roleIds);
        }

        $debugInfo = [
            'admin_id' => $adminId,
            'admin_name' => $adminName,
            'admin_role' => $adminRole,
            'admin_role_id' => $adminRoleId,
            'is_admin' => $isAdmin,
            'role_ids_array' => $adminRoleId ? explode(',', $adminRoleId) : []
        ];

        $this->ajaxReturn(['status' => 1, 'data' => $debugInfo]);
    }

    /**
     * 获取培训订单统计数据
     * 只有管理员可以访问
     */
    public function getStats()
    {
        // 权限检查：只有管理员可以访问
        $adminId = session('admin_id');
        $adminRoleId = session('admin_role_id');

        $isAdmin = false;
        if ($adminId == 1) {
            $isAdmin = true;
        } elseif ($adminRoleId) {
            $roleIds = explode(',', $adminRoleId);
            $isAdmin = in_array('1', $roleIds);
        }

        if (!$isAdmin) {
            $this->ajaxReturn(['status' => 0, 'info' => '权限不足']);
        }

        try {
            $orderModel = D('TrainingOrder');

            // 基础查询条件：排除指定的服务站和取消的订单
            $baseWhere = [
                'station_id' => ['not in', [4, 32]],
                'sub_status' => ['neq', 'terminated'] // 排除取消的订单
            ];

            // 岗位筛选条件
            $postId = I('get.post_id', 0, 'intval');
            if ($postId > 0) {
                $baseWhere['post_id'] = $postId;
            }

            // 1. 订单总数统计（排除取消的订单）
            $totalOrders = $orderModel->where($baseWhere)->count();

            // 2. 按完成状态分类统计
            // 已完成订单
            $completedOrders = $orderModel->where(array_merge($baseWhere, [
                'sub_status' => 'completed'
            ]))->count();

            // 未完成订单（总数 - 已完成）
            $incompleteOrders = $totalOrders - $completedOrders;

            // 3. 普通订单 vs 招就办订单统计（排除取消的）
            $normalOrders = $orderModel->where(array_merge($baseWhere, [
                '_complex' => [
                    'zsb_id' => ['exp', 'IS NULL'],
                    '_logic' => 'OR',
                    'zsb_id' => 0
                ]
            ]))->count();
            $zsbOrders = $orderModel->where(array_merge($baseWhere, [
                'zsb_id' => ['gt', 0]
            ]))->count();

            // 4. 总报名费统计（分转元）
            $totalFeeResult = $orderModel->where($baseWhere)->sum('fee_amount');
            $totalFee = round(($totalFeeResult ?: 0) / 100, 2);

            // 已完成订单的报名费
            $completedFeeResult = $orderModel->where(array_merge($baseWhere, [
                'sub_status' => 'completed'
            ]))->sum('fee_amount');
            $completedFee = round(($completedFeeResult ?: 0) / 100, 2);

            // 未完成订单的报名费
            $incompleteFee = $totalFee - $completedFee;

            // 4.1 已收款和待收款统计
            $paymentModel = D('PaymentRecord');

            // 获取所有有效订单的ID列表
            $orderIds = $orderModel->where($baseWhere)->getField('id', true);

            if (!empty($orderIds)) {
                // 已收款统计：所有支付记录的总和（排除退款）
                $paidAmountResult = $paymentModel->where([
                    'order_id' => ['in', $orderIds],
                    'pay_type' => ['in', ['intent', 'partial', 'full']]
                ])->sum('pay_amount');
                $paidAmount = round(($paidAmountResult ?: 0) / 100, 2);

                // 退款金额
                $refundAmountResult = $paymentModel->where([
                    'order_id' => ['in', $orderIds],
                    'pay_type' => 'refund'
                ])->sum('pay_amount');
                $refundAmount = round(($refundAmountResult ?: 0) / 100, 2);

                // 实际已收款 = 支付金额 - 退款金额
                $actualPaidAmount = $paidAmount - $refundAmount;

                // 待收款 = 总报名费 - 实际已收款
                $pendingAmount = $totalFee - $actualPaidAmount;

                // 确保待收款不为负数
                if ($pendingAmount < 0) {
                    $pendingAmount = 0;
                }
            } else {
                $actualPaidAmount = 0;
                $pendingAmount = 0;
            }

            // 5. 平台收入统计（分转元）
            // 获取所有有效订单，根据实际付款情况分类计算
            $validOrders = $orderModel->where(array_merge($baseWhere, [
                'zcgk_commission' => ['gt', 0]
            ]))->field('id, fee_amount, zcgk_commission')->select();

            $expectedTotalCommission = 0;   // 预计总收入（排除完全未付款订单）
            $actualCommission = 0;          // 实收收入（全额付款订单）
            $pendingCommission = 0;         // 待收统计（部分付款订单）

            foreach ($validOrders as $order) {
                // 计算该订单的已支付金额（排除退款）
                $paidAmount = D('PaymentRecord')->where([
                    'order_id' => $order['id'],
                    'pay_type' => ['in', ['intent', 'partial', 'full']]
                ])->sum('pay_amount') ?: 0;

                $remainingAmount = $order['fee_amount'] - $paidAmount;

                if ($paidAmount > 0) {
                    // 有付款的订单计入预计总收入
                    $expectedTotalCommission += $order['zcgk_commission'];

                    if ($remainingAmount <= 0) {
                        // 已全额付款的订单：平台收入计入实收收入
                        $actualCommission += $order['zcgk_commission'];
                    } else {
                        // 部分付款的订单：平台收入计入待收统计
                        $pendingCommission += $order['zcgk_commission'];
                    }
                }
                // 完全未付款的订单不计入任何收入统计
            }

            // 转换为元
            $expectedTotalCommission = round($expectedTotalCommission / 100, 2);
            $actualCommission = round($actualCommission / 100, 2);
            $pendingCommission = round($pendingCommission / 100, 2);

            // 订单状态收入统计
            // 已完成：服务完成的订单平台收入
            $completedCommissionResult = $orderModel->where(array_merge($baseWhere, [
                'sub_status' => 'completed',
                'zcgk_commission' => ['gt', 0]
            ]))->sum('zcgk_commission');
            $completedCommission = round(($completedCommissionResult ?: 0) / 100, 2);

            // 进行中：有效订单的平台收入（除了已完成和服务终止的）
            $ongoingCommissionResult = $orderModel->where(array_merge($baseWhere, [
                'sub_status' => ['not in', ['completed', 'terminated']],
                'zcgk_commission' => ['gt', 0]
            ]))->sum('zcgk_commission');
            $ongoingCommission = round(($ongoingCommissionResult ?: 0) / 100, 2);

            // 6. 服务站收益总额统计
            // 普通订单：reward_station_amt
            $normalStationProfitResult = $orderModel->where(array_merge($baseWhere, [
                '_complex' => [
                    'zsb_id' => ['exp', 'IS NULL'],
                    '_logic' => 'OR',
                    'zsb_id' => 0
                ]
            ]))->sum('reward_station_amt');

            // 招就办订单：station_profit + zsb_commission
            $zsbStationProfitResult = $orderModel->where(array_merge($baseWhere, [
                'zsb_id' => ['gt', 0]
            ]))->sum('station_profit');
            $zsbCommissionResult = $orderModel->where(array_merge($baseWhere, [
                'zsb_id' => ['gt', 0]
            ]))->sum('zsb_commission');

            $totalStationProfit = round((($normalStationProfitResult ?: 0) + ($zsbStationProfitResult ?: 0) + ($zsbCommissionResult ?: 0)) / 100, 2);

            // 已完成订单的服务站收益
            $completedNormalStationProfitResult = $orderModel->where(array_merge($baseWhere, [
                'sub_status' => 'completed',
                '_complex' => [
                    'zsb_id' => ['exp', 'IS NULL'],
                    '_logic' => 'OR',
                    'zsb_id' => 0
                ]
            ]))->sum('reward_station_amt');

            $completedZsbStationProfitResult = $orderModel->where(array_merge($baseWhere, [
                'sub_status' => 'completed',
                'zsb_id' => ['gt', 0]
            ]))->sum('station_profit');
            $completedZsbCommissionResult = $orderModel->where(array_merge($baseWhere, [
                'sub_status' => 'completed',
                'zsb_id' => ['gt', 0]
            ]))->sum('zsb_commission');

            $completedStationProfit = round((($completedNormalStationProfitResult ?: 0) + ($completedZsbStationProfitResult ?: 0) + ($completedZsbCommissionResult ?: 0)) / 100, 2);

            // 未完成订单的服务站收益
            $incompleteStationProfit = $totalStationProfit - $completedStationProfit;

            // 7. 按状态统计（排除取消的订单）
            $statusStats = [];
            foreach ($orderModel->main_status as $status => $info) {
                $count = $orderModel->where(array_merge($baseWhere, [
                    'main_status' => $status
                ]))->count();
                $statusStats[] = [
                    'status' => $status,
                    'name' => $info['text'],
                    'count' => $count,
                    'style' => $info['style']
                ];
            }

            // 单独统计取消的订单数量（用于显示但不计入主要统计）
            $cancelledOrders = $orderModel->where([
                'station_id' => ['not in', [4, 32]],
                'sub_status' => 'terminated'
            ])->count();

            // 8. 最近30天订单趋势（简化版，排除取消的）
            $thirtyDaysAgo = strtotime('-30 days');
            $recentOrders = $orderModel->where(array_merge($baseWhere, [
                'create_time' => ['egt', $thirtyDaysAgo]
            ]))->count();

            // 9. 获取有订单的岗位列表（用于筛选器）
            // 定义不包含岗位筛选的基础条件（用于获取所有岗位列表）
            $baseWhereForPosts = [
                'station_id' => ['not in', [4, 32]],
                'sub_status' => ['neq', 'terminated'] // 排除取消的订单
            ];

            // 先获取有订单的岗位ID（不包含岗位筛选条件，获取所有有订单的岗位）
            $postIdsWithOrders = $orderModel->where($baseWhereForPosts)->group('post_id')->getField('post_id', true);

            if (!empty($postIdsWithOrders)) {
                $postList = D('ProjectPost')
                    ->alias('pp')
                    ->join('LEFT JOIN __PROJECT__ p ON pp.project_id = p.id')
                    ->where([
                        'pp.id' => ['in', $postIdsWithOrders]
                    ])
                    ->field('pp.id, pp.job_name, p.name as project_name, pp.project_id')
                    ->order('p.name ASC, pp.job_name ASC')
                    ->select();

                // 统计每个岗位的订单数量（使用相同的基础条件）
                $postOrderCounts = [];
                foreach ($postList as $post) {
                    $count = $orderModel->where(array_merge($baseWhereForPosts, [
                        'post_id' => $post['id']
                    ]))->count();
                    $postOrderCounts[$post['id']] = $count;
                }

                // 按项目分组岗位，并包含订单数量（只包含有订单的岗位）
                $groupedPosts = [];
                foreach ($postList as $post) {
                    $orderCount = $postOrderCounts[$post['id']];
                    // 只显示有订单的岗位
                    if ($orderCount > 0) {
                        $projectName = $post['project_name'];
                        if (!isset($groupedPosts[$projectName])) {
                            $groupedPosts[$projectName] = [];
                        }
                        $groupedPosts[$projectName][] = [
                            'id' => $post['id'],
                            'name' => $post['job_name'],
                            'project_id' => $post['project_id'],
                            'order_count' => $orderCount
                        ];
                    }
                }
            } else {
                $groupedPosts = [];
            }

            $this->ajaxReturn([
                'status' => 1,
                'data' => [
                    // 基础统计
                    'total_orders' => $totalOrders,
                    'completed_orders' => $completedOrders,
                    'incomplete_orders' => $incompleteOrders,
                    'cancelled_orders' => $cancelledOrders,

                    // 订单类型统计
                    'normal_orders' => $normalOrders,
                    'zsb_orders' => $zsbOrders,

                    // 收入统计
                    'total_fee' => $totalFee,
                    'completed_fee' => $completedFee,
                    'incomplete_fee' => $incompleteFee,

                    // 收款统计
                    'paid_amount' => $actualPaidAmount,
                    'pending_amount' => $pendingAmount,

                    'expected_total_commission' => $expectedTotalCommission,
                    'actual_commission' => $actualCommission,
                    'pending_commission' => $pendingCommission,
                    'completed_commission' => $completedCommission,
                    'ongoing_commission' => $ongoingCommission,

                    'total_station_profit' => $totalStationProfit,
                    'completed_station_profit' => $completedStationProfit,
                    'incomplete_station_profit' => $incompleteStationProfit,

                    // 其他统计
                    'status_stats' => $statusStats,
                    'recent_orders' => $recentOrders,
                    'update_time' => date('Y-m-d H:i:s'),

                    // 筛选相关
                    'post_list' => $groupedPosts,
                    'current_post_id' => $postId
                ]
            ]);

        } catch (\Exception $e) {
            \Think\Log::write('获取培训订单统计数据失败：' . $e->getMessage(), 'ERROR');
            $this->ajaxReturn(['status' => 0, 'info' => '获取统计数据失败']);
        }
    }

    /**
     * 订单详情
     */
    public function detail() {
        $id = intval(I('get.id'));
        if (!$id) $this->error('参数错误');

        // 获取订单信息
        $orderModel = D("TrainingOrder");
        $order = $orderModel->where(['id' => $id])->find();
        if (!$order) $this->error('订单不存在');

        // 获取关联信息 - 修正数据获取逻辑以保持与index页面一致
        $user = D('User')->where(['id' => $order['user_id']])->find();

        // 优先使用简历信息，如果订单中有关联简历ID
        $userJob = null;
        if (!empty($order['user_job_id'])) {
            $userJob = D('UserJob')->where(['id' => $order['user_job_id']])->find();
        }

        // userJob输出到日志
        \Think\Log::write('userJob: ' . json_encode($userJob), 'INFO');

        // 根据简历信息或用户信息设置显示数据
        if ($userJob) {
            $user['display_realname'] = $userJob['name'];
            $user['display_mobile'] = $userJob['phone'];
            $user['idcard'] = $userJob['id_number']; // 从简历获取身份证号
            $user['from_resume'] = true;
        } else {
            $user['display_realname'] = $user['nickname']; // 使用nickname字段与index页面保持一致
            $user['display_mobile'] = $user['mobile'];
            $user['from_resume'] = false;
        }

        $post = D('ProjectPost')->where(['id' => $order['post_id']])->find();

        // 如果是招就办订单，需要获取招就办配置的价格信息
        $zsbPriceConfig = null;
        if ($post && !empty($order['zsb_id'])) {
            $zsbPriceConfig = D('ZsbPostPrice')->where([
                'zsb_id' => $order['zsb_id'],
                'post_id' => $order['post_id'],
                'status' => 1
            ])->find();

            if ($zsbPriceConfig) {
                // 使用招就办配置的价格
                if ($post['is_free'] == 1) {
                    // 公益项目
                    $post['service_price'] = 0;
                    $post['max_price'] = 0;
                    $post['service_price_text'] = '公益实习';
                    $post['max_price_text'] = '公益实习';
                } else {
                    // 招就办配置价格（从分转换为元）
                    $salePriceYuan = round($zsbPriceConfig['sale_price'] / 100, 2);

                    // 对于招就办订单，只显示一个价格（对外价格）
                    $post['service_price'] = $salePriceYuan;
                    $post['service_price_text'] = '';

                    // 将max_price设为0，避免显示重复价格
                    $post['max_price'] = 0;
                    $post['max_price_text'] = '';
                }

                \Think\Log::write('招就办订单价格处理: order_id=' . $order['id'] .
                    ', post_id=' . $post['id'] . ', sale_price=' . $post['service_price'] . '元', 'INFO');
            }
        }

        // 确保岗位价格字段存在
        if ($post) {
            $post['name'] = $post['job_name']; // 确保name字段存在
            $post['price'] = $post['service_price']; // 确保price字段存在

            // 确保价格文本字段存在（仅对非招就办订单或未配置价格的情况）
            if (empty($order['zsb_id']) || empty($zsbPriceConfig)) {
                if (!isset($post['service_price_text']) || empty($post['service_price_text'])) {
                    $post['service_price_text'] = '本科及以上学历';
                }
                if (!isset($post['max_price_text']) || empty($post['max_price_text'])) {
                    $post['max_price_text'] = '本科以下学历';
                }
            }
        }

        $project = D('Project')->where(['id' => $post['project_id']])->find();
        $station = D('ServiceStation')->where(['id' => $order['station_id']])->find();
        $parentStation = $order['parent_station_id'] ? D('ServiceStation')->where(['id' => $order['parent_station_id']])->find() : null;

        // 获取招就办信息（如果是招就办订单）
        $zsbInfo = null;
        if (!empty($order['zsb_id']) && $order['zsb_id'] > 0) {
            $zsbInfo = D('ServiceStation')->where(['id' => $order['zsb_id']])->find();
        }

        // 获取沟通记录（如果有关联的简历）
        $recentMessagesList = [];
        $messageCount = 0;
        if (!empty($order['user_job_id'])) {
            $recentMessages = D("ServiceStationPlatformMessage")
                ->where(['user_job_id' => $order['user_job_id']])
                ->order('create_time DESC')
                ->limit(5)
                ->select();

            if ($recentMessages) {
                $recentMessagesList = $recentMessages;
                // 获取总消息数量
                $messageCount = D("ServiceStationPlatformMessage")
                    ->where(['user_job_id' => $order['user_job_id']])
                    ->count();
            }
        }

        // 获取支付记录（使用新的方法）
        $paymentRecords = D('PaymentRecord')->getRecordsByOrderId($id);

        // 获取分阶段付款金额
        $paymentAmounts = D('PaymentRecord')->getOrderPaymentAmounts($id);

        // 将付款金额合并到订单数据中
        $order = array_merge($order, $paymentAmounts);

        // 注释：改为依赖表数据，移除实时计算逻辑
        // 招就办订单的收益信息直接使用数据库存储的字段
        if (!empty($order['zsb_id']) && $order['zsb_id'] > 0) {
            // 使用数据库存储的收益字段，无需重新计算
            // station_profit: 服务站收益（分单位）
            // zsb_commission: 招就办收益（分单位）
            // reward_station_amt 保持原值，用于显示总收益

            // 设置前端需要的详细收益字段
            $order['station_profit_detail'] = !empty($order['station_profit']) ? $order['station_profit'] : 0;
            $order['zsb_commission_detail'] = !empty($order['zsb_commission']) ? $order['zsb_commission'] : 0;

            \Think\Log::write('订单id: '.$order['zsb_id'].' ,招就办订单使用存储的收益数据 station_profit='.$order['station_profit'].' zsb_commission='.$order['zsb_commission']);
        }

        // 解决左侧菜单问题：手动设置当前菜单
        $menuModel = D("Menu");

        // 查找Training/detail菜单项
        $menuItem = $menuModel->where(['app' => 'Prime', 'model' => 'Training', 'action' => 'detail'])->find();

        if (!$menuItem) {
            // 如果找不到detail菜单项，尝试找index菜单项作为基础
            $menuItem = $menuModel->where(['app' => 'Prime', 'model' => 'Training', 'action' => 'index'])->find();
        }

        if ($menuItem) {
            // 手动构建完整的菜单信息
            $parentId = $menuItem['parentid'];
            $bootId = 0;

            // 向上查找父级菜单，直到找到根菜单（parentid=0的菜单）
            if ($parentId > 0) {
                $bootId = $menuModel->getBoot($parentId);
            } else {
                $bootId = $menuItem['id']; // 如果当前就是顶级菜单
            }

            // 构建完整的菜单信息
            $menuInfo = [
                'id' => $menuItem['id'],
                'parentid' => $parentId,
                'boot_id' => $bootId ? $bootId : $menuItem['id'] // 如果bootId为0，则使用当前菜单ID
            ];

            // 重新分配菜单变量到模板
            $this->assign('cur_menu', $menuInfo);

            // 确保main_menus变量中包含这个boot_id
            $mainMenus = session('main_menus');
            if ($mainMenus && !isset($mainMenus[$menuInfo['boot_id']])) {
                // 获取菜单树并重新设置session
                $mainMenus = $menuModel->menu_json();
                session('main_menus', $mainMenus);
            }
        }

        // 模板赋值
        $this->assign('order', $order);
        $this->assign('user', $user);
        $this->assign('post', $post);
        $this->assign('project', $project);
        $this->assign('station', $station);
        $this->assign('parent_station', $parentStation);
        $this->assign('zsb_info', $zsbInfo);
        $this->assign('zsb_price_config', $zsbPriceConfig); // 传递招就办价格配置
        $this->assign('payment_records', $paymentRecords);
        $this->assign('recent_messages_list', $recentMessagesList); // 沟通记录
        $this->assign('message_count', $messageCount); // 消息总数

        // 只使用新的二级状态信息
        $this->assign('main_status', $orderModel->main_status);
        $this->assign('sub_status', $orderModel->sub_status);

        $this->display();
    }

    /**
     * 培训订单快速回复消息
     */
    public function quickReply() {
        // 记录请求日志
        dolog('training/quickreply/request', 'Training QuickReply request received');

        // 设置响应头为JSON格式
        header('Content-Type: application/json; charset=utf-8');

        if (!IS_POST) {
            dolog('training/quickreply/error', 'Invalid request method: ' . $_SERVER['REQUEST_METHOD']);
            echo json_encode(['status' => 0, 'info' => '请求方式错误']);
            exit;
        }

        $user_job_id = intval(I('post.user_job_id'));
        $content = trim(I('post.content'));
        $need_reply = I('post.need_reply');
        $send_wechat = I('post.send_wechat');

        // 记录参数日志
        dolog('training/quickreply/params', 'user_job_id=' . $user_job_id . ', content_length=' . strlen($content));

        if (!$user_job_id) {
            echo json_encode(['status' => 0, 'info' => '简历ID不能为空']);
            exit;
        }

        if (empty($content)) {
            echo json_encode(['status' => 0, 'info' => '回复内容不能为空']);
            exit;
        }

        // 获取简历信息
        $jobRow = D("UserJob")->where(['id' => $user_job_id])->find();
        if (!$jobRow) {
            echo json_encode(['status' => 0, 'info' => '简历不存在']);
            exit;
        }

        // 获取服务站信息
        $serviceStationRow = D("ServiceStation")->where(['id' => $jobRow['service_station_id']])->find();

        // 创建消息记录
        $obj = D("ServiceStationPlatformMessage");
        $data = $obj->create([
            'user_job_id' => $user_job_id,
            'content' => $content,
            'type' => 2, // 平台消息
            'create_time' => time()
        ]);

        if ($data) {
            // 添加消息记录
            $messageId = $obj->add($data);

            // 记录插入结果
            dolog('training/quickreply/insert_result', 'Message inserted with ID: ' . $messageId);

            if ($messageId) {
                // 更新简历状态
                $updateData = [
                    'is_reply' => 1,
                    'msg_reply_time' => time()
                ];

                // 处理提醒回复功能
                if (I('post.need_reply') == '1') {
                    $updateData['need_reply'] = 1;

                    // 发送短信提醒给服务站
                    if ($serviceStationRow && !empty($serviceStationRow['mobile']) && !empty($jobRow['name'])) {
                        try {
                            $aliSms = new \Util\AliSms();
                            // 使用现有的短信模板发送提醒
                            $smsText = "您好，简历用户【{$jobRow['name']}】有新的平台消息需要回复，请及时查看处理。";
                            $smsResult = $aliSms->sendSms($smsText, $serviceStationRow['mobile'], 'SMS_479770174', '{"code":"' . $smsText . '"}');

                            if ($smsResult) {
                                dolog('training/sms/quickreply_reminder_success', "服务站: {$serviceStationRow['service_name']}, 简历用户: {$jobRow['name']}, 时间: " . date('Y-m-d H:i:s'));
                            } else {
                                dolog('training/sms/quickreply_reminder_failed', "服务站: {$serviceStationRow['service_name']}, 简历用户: {$jobRow['name']}, 时间: " . date('Y-m-d H:i:s'));
                            }
                        } catch (Exception $e) {
                            dolog('training/sms/quickreply_reminder_exception', "服务站: {$serviceStationRow['service_name']}, 简历用户: {$jobRow['name']}, 错误: " . $e->getMessage());
                        }
                    }
                } else {
                    $updateData['need_reply'] = 0;
                }

                // 更新简历状态
                $updateResult = D("UserJob")->where(['id' => $user_job_id])->save($updateData);

                if ($updateResult !== false) {
                    // 处理微信通知功能
                    if (I('post.send_wechat') == '1') {
                        // 根据服务站类型获取微信群聊配置
                        $fwzservice_name = $serviceStationRow['service_name'];
                        $chatroom = $serviceStationRow['chatroom'];
                        $wxatuserlist = $serviceStationRow['wxatuserlist'];
                        $zjbname = '';
                        if ($serviceStationRow['zsb_type'] == 2) {
                            $zjbname = '【招就办-'.$serviceStationRow['contract_name'].'】';
                        }

                        // 如果当前服务站类型为2，则从关联的服务站获取微信配置
                        if ($serviceStationRow['zsb_type'] == 2 && !empty($serviceStationRow['zsb_ref_station'])) {
                            $refStationRow = D("ServiceStation")->where(['id' => $serviceStationRow['zsb_ref_station']])->find();
                            if ($refStationRow) {
                                $fwzservice_name = $refStationRow['service_name'];
                                $chatroom = $refStationRow['chatroom'];
                                $wxatuserlist = $refStationRow['wxatuserlist'];
                            }
                        }

                        // 检查微信配置是否完整
                        if (!empty($chatroom) && !empty($wxatuserlist)) {
                            // 构建微信通知内容
                            $smscontent = $fwzservice_name . "，平台回复了".$zjbname."【{$jobRow['name']}】简历，请及时查看处理。\n回复内容如下：\n{$data['content']}";

                            try {
                                $this->sendWechatNotification($smscontent, $chatroom, $wxatuserlist);
                                dolog('training/quickreply/wechat_success', 'WeChat notification sent for user_job_id=' . $user_job_id);
                            } catch (Exception $e) {
                                dolog('training/quickreply/wechat_error', 'WeChat notification failed for user_job_id=' . $user_job_id . ', error: ' . $e->getMessage());
                            }
                        } else {
                            dolog('training/quickreply/wechat_skip', 'WeChat notification skipped for user_job_id=' . $user_job_id . ' - missing chatroom or wxatuserlist data in service station');
                        }
                    }

                    // 返回成功响应
                    dolog('training/quickreply/success', 'QuickReply success: message_id=' . $messageId . ', user_job_id=' . $user_job_id);
                    echo json_encode([
                        'status' => 1,
                        'info' => '快速回复发送成功',
                        'data' => [
                            'message_id' => $messageId,
                            'user_job_id' => $user_job_id
                        ]
                    ]);
                } else {
                    dolog('training/quickreply/error', 'Failed to update UserJob status for user_job_id=' . $user_job_id);
                    echo json_encode(['status' => 0, 'info' => '更新简历状态失败，请重试']);
                }
            } else {
                // 消息添加失败
                dolog('training/quickreply/error', 'Failed to add message record for user_job_id=' . $user_job_id);
                echo json_encode(['status' => 0, 'info' => '回复失败，请重试']);
            }
        } else {
            // 数据验证失败
            $error = $obj->getError();
            dolog('training/quickreply/create_error', 'Data validation failed: ' . $error);
            echo json_encode(['status' => 0, 'info' => $error ?: '数据验证失败，请检查输入内容']);
        }
        exit;
    }

    /**
     * 发送微信消息通知
     * @param string $content 回复内容
     * @param string $chatroom 群聊ID
     * @param string $wxatuserlist @用户列表
     */
    private function sendWechatNotification($content, $chatroom, $wxatuserlist) {
        try {
            // 构建POST数据
            $postData = [
                'WxID' => 'zcgk666',
                'Data' => [
                    'SendTo' => $chatroom,
                    'Msg' => '@' . $content,
                    'AtUserList' => $wxatuserlist,
                    'ArgList' => ['SendTo', 'Msg', 'AtUserList'],
                    'AnyCallName' => 'SendMessageAt',
                    'TimeOut' => -1,
                    'CallName' => 'Any'
                ],
                'CallBackUrl' => null
            ];

            // 转换为JSON格式
            $jsonData = json_encode($postData, JSON_UNESCAPED_UNICODE);

            // 记录发送日志
            dolog('training/wechat_notification/send', 'Sending notification: ' . $jsonData);

            // 初始化cURL
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, 'http://43.139.30.237:1080/HTTPManagement.aspx?Item=AddMessage');
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonData);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 10);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json',
                'Content-Length: ' . strlen($jsonData)
            ]);

            // 执行请求
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);

            // 记录响应日志
            dolog('training/wechat_notification/response', 'HTTP Code: ' . $httpCode . ', Response: ' . $response . ', Error: ' . $error);

            if ($error) {
                throw new Exception('cURL Error: ' . $error);
            }

            if ($httpCode !== 200) {
                throw new Exception('HTTP Error: ' . $httpCode);
            }

            return true;
        } catch (Exception $e) {
            // 记录异常日志
            dolog('training/wechat_notification/exception', 'Exception: ' . $e->getMessage());
            return false;
        }
    }



    /**
     * 审核通过
     */
    public function approve() {
        $id = intval(I('get.id'));
        if (!$id) $this->error('参数错误');

        $orderModel = D("TrainingOrder");
        $order = $orderModel->where(['id' => $id])->find();
        if (!$order) $this->error('订单不存在');

        // 只有初步沟通状态的订单可以审核通过
        if ($order['sub_status'] != 'initial_contact') {
            $this->error('只有初步沟通状态的订单可以审核通过');
        }

        // 检查是否为公益项目
        $post = D('ProjectPost')->where(['id' => $order['post_id']])->find();
        if ($post && $post['is_free'] == 1) {
            // 公益项目：直接跳过付款流程
            try {
                // 开启事务
                $orderModel->startTrans();

                // 使用新的状态管理系统：更新为已全额付款
                $result = $orderModel->updateOrderStatusV2(
                    $id,
                    'communication',
                    'full_paid',
                    ['fee_amount' => $order['fee_amount'] / 100], // 转换为元单位
                    session('admin_id'),
                    '公益项目审核通过，自动标记为已付款'
                );
                if (!$result) {
                    throw new \Exception('更新订单状态失败');
                }

                // 支付状态由新的状态管理系统自动处理

                // 创建0元支付记录
                $payData = [
                    'order_id' => $id,
                    'pay_channel' => 'free', // 公益项目标记
                    'pay_amount' => 0,
                    'pay_time' => time(),
                ];
                $paymentModel = D('PaymentRecord');
                $payId = $paymentModel->createRecord($payData);
                if (!$payId) {
                    throw new \Exception('创建支付记录失败');
                }

                // 计算奖励金额（虽然是0）
                $this->calculateRewardAndGetResults($id);

                // 公益项目不需要冻结奖励，因为奖励为0

                $orderModel->commit();
                \Think\Log::write('公益项目审核通过，直接跳过付款流程: order_id=' . $id, 'INFO');
                $this->success('公益项目审核通过成功，已自动跳过付款流程', U('training/detail', ['id' => $id]));
            } catch (\Exception $e) {
                $orderModel->rollback();
                \Think\Log::write('公益项目审核通过失败: ' . $e->getMessage(), 'ERROR');
                $this->error('操作失败：' . $e->getMessage());
            }
        } else {
            // 非公益项目：正常流程，更新为已签合同待付款
            $result = $orderModel->updateOrderStatusV2(
                $id,
                'communication',
                'contract_signed',
                [],
                session('admin_id'),
                '审核通过，等待付款'
            );
            if ($result) {
                $this->success('审核通过成功', U('training/detail', ['id' => $id]));
            } else {
                $this->error('操作失败');
            }
        }
    }

    /**
     * 审核驳回
     */
    public function reject() {
        $id = intval(I('get.id'));
        if (!$id) $this->error('参数错误');

        // 权限验证：只有管理员可以审核驳回
        if (!session('admin_id')) {
            $this->error('只有管理员可以审核驳回');
        }

        $orderModel = D("TrainingOrder");
        $order = $orderModel->where(['id' => $id])->find();
        if (!$order) $this->error('订单不存在');

        // 只有初步沟通状态的订单可以审核驳回
        if ($order['sub_status'] != 'initial_contact') {
            $this->error('只有初步沟通状态的订单可以审核驳回');
        }

        // 使用新的状态管理系统：更新为服务终止
        $result = $orderModel->updateOrderStatusV2(
            $id,
            'service_review',
            'terminated',
            [],
            session('admin_id'),
            '审核驳回'
        );
        if ($result) {
            $this->success('审核驳回成功', U('training/detail', ['id' => $id]));
        } else {
            $this->error('操作失败');
        }
    }

    /**
     * 确认收款
     */
    public function confirmPayment() {
        $id = intval(I('get.id'));
        if (!$id) $this->error('参数错误');

        $orderModel = D("TrainingOrder");
        $order = $orderModel->where(['id' => $id])->find();
        if (!$order) $this->error('订单不存在');

        // 只有待付款状态的订单可以确认收款
        if ($order['order_status'] != 'pending_payment') {
            $this->error('只有待付款状态的订单可以确认收款');
        }

        // 获取已支付金额和剩余应付金额
        $paymentAmounts = D('PaymentRecord')->getOrderPaymentAmounts($id);
        $paidAmount = $paymentAmounts['total_paid'] ?? 0; // 分单位
        $remainingAmount = $order['fee_amount'] - $paidAmount; // 分单位

        if ($remainingAmount <= 0) {
            $this->error('该订单已全额付款，无需再次确认收款');
        }

        if (IS_POST) {
            try {
                // 开启事务
                $orderModel->startTrans();

                // 创建支付记录（使用剩余应付金额）
                $payData = [
                    'order_id' => $id,
                    'pay_channel' => I('post.pay_channel'),
                    'pay_amount' => $remainingAmount, // 使用剩余应付金额，而不是总报名费
                    'pay_type' => 'full', // 设置为全额付款
                    'pay_time' => time(),
                    'remark' => '确认收款 - 剩余金额：' . number_format($remainingAmount / 100, 2) . '元'
                ];

                $paymentModel = D('PaymentRecord');
                $payId = $paymentModel->createRecord($payData);

                if (!$payId) {
                    throw new \Exception('创建支付记录失败');
                }

                // 使用新的状态管理系统：更新为已全额付款
                // 注意：不传递fee_amount，避免支付金额覆盖订单报名费
                $result = $orderModel->updateOrderStatusV2(
                    $id,
                    'communication',
                    'full_paid',
                    [], // 不传递金额信息，保持订单原有报名费不变
                    session('admin_id'),
                    '确认收款',
                    I('post.pay_channel') // 传递支付渠道
                );

                if (!$result) {
                    throw new \Exception('更新订单状态失败');
                }

                // 计算奖励金额
                $this->calculateRewardAndGetResults($id);

                // 冻结资金（根据订单类型选择不同的冻结逻辑）
                if (!empty($order['zsb_id']) && $order['zsb_id'] > 0) {
                    // 招就办订单：冻结服务站总收益（station_profit + zsb_commission）
                    $this->freezeStationReward($id);
                } else {
                    // 普通订单：冻结服务站奖励
                    $this->freezeStationReward($id);
                }

                // 提交事务
                $orderModel->commit();

                $this->success('确认收款成功', U('training/detail', ['id' => $id]));
            } catch (\Exception $e) {
                $orderModel->rollback();
                \Think\Log::write('确认收款失败：' . $e->getMessage() . ' order_id=' . $id, 'ERROR');
                $this->error('操作失败：' . $e->getMessage());
            }
        } else {
            // 将支付信息传递给视图
            $order['remaining_amount'] = $remainingAmount; // 分单位
            $order['remaining_amount_yuan'] = $remainingAmount / 100; // 元单位
            $order['paid_amount'] = $paidAmount; // 分单位
            $order['paid_amount_yuan'] = $paidAmount / 100; // 元单位

            $this->assign('order', $order);
            $this->assign('pay_channel', D('PaymentRecord')->pay_channel);
            $this->display();
        }
    }

    /**
     * 标记培训开始
     */
    public function startTraining() {
        $id = intval(I('get.id'));
        if (!$id) $this->error('参数错误');

        $orderModel = D("TrainingOrder");
        $order = $orderModel->where(['id' => $id])->find();
        if (!$order) $this->error('订单不存在');

        // 只有已全额付款状态的订单可以标记培训开始
        if ($order['sub_status'] != 'full_paid') {
            $this->error('只有已全额付款状态的订单可以标记培训开始');
        }

        // 使用新的状态管理系统：更新为参与培训中
        $result = $orderModel->updateOrderStatusV2(
            $id,
            'training',
            'in_training',
            [],
            session('admin_id'),
            '管理员标记培训开始'
        );

        if ($result) {
            $this->success('标记培训开始成功', U('training/detail', ['id' => $id]));
        } else {
            $this->error('操作失败');
        }
    }



    /**
     * 标记培训完成/入职
     */
    public function complete() {
        $id = intval(I('get.id'));
        if (!$id) $this->error('参数错误');

        $orderModel = D("TrainingOrder");
        $order = $orderModel->where(['id' => $id])->find();
        if (!$order) $this->error('订单不存在');

        // 只有参与培训中状态的订单可以标记完成
        if ($order['sub_status'] != 'in_training') {
            $this->error('只有参与培训中状态的订单可以标记完成');
        }

        if (IS_POST) {
            $completeType = I('post.complete_type', 'completed');

            // 使用新的状态管理系统：更新为服务完成
            $result = $orderModel->updateOrderStatusV2(
                $id,
                'service_review',
                'completed',
                [],
                session('admin_id'),
                '标记培训完成'
            );
            if ($result) {
                // 奖励状态由新的状态管理系统自动处理

                // 根据完成类型处理奖励
                $this->unlockReward($id, $completeType);

                // 更新用户状态
                $userStatus = ($completeType == 'employed') ? 2 : 3; // 2=已入职, 3=服务终止
                D('User')->where(['id' => $order['user_id']])->save(['service_status' => $userStatus]);

                // **新增：同步更新相关简历状态**
                $jobStatus = ($completeType == 'employed') ? 2 : 3; // 2=已入职, 3=服务终止
                if (!empty($order['user_job_id'])) {
                    // 如果订单关联了特定简历，只更新该简历状态
                    D('UserJob')->where(['id' => $order['user_job_id']])->save(['job_state' => $jobStatus]);
                } else {
                    // 如果没有关联简历，更新该用户所有简历的状态
                    $userInfo = D('User')->where(['id' => $order['user_id']])->find();
                    if ($userInfo && !empty($userInfo['mobile'])) {
                        D('UserJob')->where(['phone' => $userInfo['mobile']])->save(['job_state' => $jobStatus]);
                    }
                }

                $statusText = ($completeType == 'employed') ? '已入职' : '服务终止';
                $this->success('标记培训完成成功，状态：' . $statusText, U('training/detail', ['id' => $id]));
            } else {
                $this->error('操作失败');
            }
        } else {
            $this->assign('order', $order);
            $this->display();
        }
    }
    /**
     * 计算奖励金额
     * @param int $orderId 订单ID
     * @return bool 成功返回true，失败返回false
     */
    /**
     * 计算奖励金额并返回计算结果
     * @param int $orderId 订单ID
     * @return array 计算结果数组
     */
    private function calculateRewardAndGetResults($orderId) {
        $orderModel = D("TrainingOrder");
        $order = $orderModel->where(['id' => $orderId])->find();
        if (!$order) return false;

        // 获取岗位信息
        $post = D('ProjectPost')->where(['id' => $order['post_id']])->find();
        if (!$post) return false;

        // 检查是否为公益项目
        if ($post['is_free'] == 1) {
            // 公益项目：报名费和奖励均为0
            $updateData = [
                'reward_station_amt' => 0,
                'reward_parent_amt' => 0,
                'update_time' => time(),
            ];
            $orderModel->where(['id' => $orderId])->save($updateData);

            return [
                'projectIdentityCost' => 0, // 基准成本价
                'servicePrice' => 0,
                'maxPrice' => 0,
                'feeAmount' => 0, // 报名费
                'platformRate' => 0, // 平台费率
                'platformFee' => 0, // 平台服务费
                'stationReward' => 0, // 服务站收益
                'parentReward' => 0,
                'hasParentStation' => !empty($order['parent_station_id']),
                'isFree' => true,
            ];
        }

        // 获取项目身份成本
        $projectIdentityCost = 0;
        $projectJoinIdentity = D("ProjectJoinIdentity")->where([
            'project_id' => $post['project_id'],        // 项目ID
            'project_post_id' => $order['post_id'],     // 岗位ID
            'project_identity_id' => 3, // 假设使用ID为3的身份，与IndexController中一致
        ])->find();

        if ($projectJoinIdentity) {
            $projectIdentityCost = $projectJoinIdentity['cost'];
        }

        // 使用新的计算公式：服务站收益 = 报名费 - 基准成本价 - 平台服务费
        $servicePrice = $post['service_price']; // 服务价格（元单位）
        $maxPrice = $post['max_price']; // 最高价格（元单位）
        $feeAmount = $order['fee_amount']; // 用户选择的报名费（分单位）
        $feeAmountYuan = round($feeAmount / 100, 2); // 转换为元单位用于计算

        // 获取平台费率
        $platformRate = $this->getPlatformRate();

        // 计算平台服务费：max(0, 报名费 - service_price) * platform_rate
        $platformFee = max(0, $feeAmountYuan - $servicePrice) * $platformRate;

        // 新公式：服务站收益 = 报名费 - 基准成本价 - 平台服务费
        $stationReward = $feeAmountYuan - $projectIdentityCost - $platformFee;

        // 确保服务站收益不为负数
        if ($stationReward < 0) {
            $stationReward = 0;
        }

        // 记录计算过程
        \Think\Log::write('新奖励计算 - 报名费: ' . $feeAmountYuan . '元, 基准成本价: ' . $projectIdentityCost . '元, 平台服务费: ' . $platformFee . '元, 服务站收益: ' . $stationReward . '元', 'INFO');

        // 上级服务站奖励直接使用reward字段
        $parentReward = 0;
        if ($order['parent_station_id']) {
            $parentReward = $post['reward'];
        }

        // 更新订单奖励金额（转换为分单位存储，数据库字段注释为分单位）
        $updateData = [
            'reward_station_amt' => intval($stationReward * 100),
            'reward_parent_amt' => intval($parentReward * 100),
            'update_time' => time(),
        ];

        $orderModel->where(['id' => $orderId])->save($updateData);

        // 返回计算结果数组
        return [
            'projectIdentityCost' => $projectIdentityCost, // 基准成本价
            'servicePrice' => $servicePrice,
            'maxPrice' => $maxPrice,
            'feeAmount' => $feeAmountYuan, // 报名费（元单位）
            'platformRate' => $platformRate, // 平台费率
            'platformFee' => $platformFee, // 平台服务费
            'stationReward' => $stationReward, // 服务站收益
            'parentReward' => $parentReward,
            'hasParentStation' => !empty($order['parent_station_id']),
            'isFree' => false,
        ];
    }

    /**
     * 计算奖励金额
     * @param int $orderId 订单ID
     * @return bool 成功返回true，失败返回false
     */
    private function calculateReward($orderId) {
        $results = $this->calculateRewardAndGetResults($orderId);
        if (!$results) return false;

        return true;
    }

    /**
     * 冻结直属服务站奖励
     * @param int $orderId 订单ID
     * @return bool 成功返回true，失败返回false
     */
    private function freezeStationReward($orderId) {
        $orderModel = D("TrainingOrder");
        $order = $orderModel->where(['id' => $orderId])->find();
        if (!$order) return false;

        // 检查是否为招就办订单
        if (!empty($order['zsb_id']) && $order['zsb_id'] > 0) {
            // 招就办订单：分别冻结服务站收益和招就办收益
            return $this->freezeZjbOrderReward($orderId, $order);
        } else {
            // 普通订单：使用传统奖励冻结逻辑
            return $this->freezeNormalOrderReward($orderId, $order);
        }
    }

    /**
     * 冻结招就办订单奖励
     * @param int $orderId 订单ID
     * @param array $order 订单信息
     * @return bool 成功返回true，失败返回false
     */
    private function freezeZjbOrderReward($orderId, $order) {
        if (empty($order['station_profit']) && empty($order['zsb_commission'])) {
            \Think\Log::write('招就办订单无收益需要冻结 order_id=' . $orderId, 'INFO');
            return true;
        }

        try {
            // 开启事务
            $serviceStationModel = D('ServiceStation');
            $stationMoneyModel = D('StationMoney');

            $serviceStationModel->startTrans();

            // 1. 冻结服务站总收益（station_profit + zsb_commission）
            $stationTotalReward = $order['station_profit'] + $order['zsb_commission']; // 分单位
            if ($stationTotalReward > 0) {
                // 使用行级锁更新服务站冻结余额
                $stationInfo = $serviceStationModel->where(['id' => $order['station_id']])->lock(true)->find();
                if (!$stationInfo) {
                    throw new \Exception('服务站信息不存在');
                }

                // 将分转换为元（z_service_station表存储的是元单位）
                $stationTotalRewardYuan = $stationTotalReward / 100;

                // 增加服务站冻结余额和总金额
                $freezeResult = $serviceStationModel->where(['id' => $order['station_id']])
                    ->save([
                        'freeze_price' => ['exp', 'freeze_price + ' . $stationTotalRewardYuan],
                        'total_price' => ['exp', 'total_price + ' . $stationTotalRewardYuan]
                    ]);

                if ($freezeResult === false) {
                    throw new \Exception('更新服务站冻结余额失败');
                }

                // 记录服务站资金明细（z_station_money表存储的是元单位）
                $stationMoneyData = [
                    'service_station_id' => $order['station_id'],
                    'type' => 5, // 培训奖励(冻结)
                    'money' => $stationTotalRewardYuan, // 元单位
                    'create_time' => time(),
                    'remark' => '确认收款-招就办培训奖励 ¥' . number_format($stationTotalReward / 100, 2) . ' 已添加到冻结余额（含招就办佣金） [订单:' . $orderId . ']',
                ];

                $moneyResult = $stationMoneyModel->add($stationMoneyData);
                if (!$moneyResult) {
                    throw new \Exception('记录服务站资金明细失败');
                }

                \Think\Log::write('服务站总收益冻结成功 station_id=' . $order['station_id'] . ', total_amount=' . $stationTotalReward . '分（' . $stationTotalRewardYuan . '元，含招就办佣金）', 'INFO');
            }

            // 2. 冻结招就办收益（不可提现，仅统计）
            if ($order['zsb_commission'] > 0) {
                // 使用行级锁更新招就办冻结余额
                $zsbInfo = $serviceStationModel->where(['id' => $order['zsb_id']])->lock(true)->find();
                if (!$zsbInfo) {
                    throw new \Exception('招就办信息不存在');
                }

                // 将分转换为元（z_service_station表存储的是元单位）
                $zsbCommissionYuan = $order['zsb_commission'] / 100;

                // 增加招就办冻结余额和总金额（注意：招就办收益不可提现）
                $zsbFreezeResult = $serviceStationModel->where(['id' => $order['zsb_id']])
                    ->save([
                        'freeze_price' => ['exp', 'freeze_price + ' . $zsbCommissionYuan],
                        'total_price' => ['exp', 'total_price + ' . $zsbCommissionYuan]
                    ]);

                if ($zsbFreezeResult === false) {
                    throw new \Exception('更新招就办冻结余额失败');
                }

                // 记录招就办资金明细（z_station_money表存储的是元单位）
                $zsbMoneyData = [
                    'service_station_id' => $order['zsb_id'],
                    'type' => 13, // 招就办佣金(冻结)
                    'money' => $zsbCommissionYuan, // 元单位
                    'create_time' => time(),
                    'remark' => '确认收款-招就办佣金 ¥' . number_format($order['zsb_commission'] / 100, 2) . ' 已添加到冻结余额（不可提现） [订单:' . $orderId . ']',
                ];

                $zsbMoneyResult = $stationMoneyModel->add($zsbMoneyData);
                if (!$zsbMoneyResult) {
                    throw new \Exception('记录招就办资金明细失败');
                }

                \Think\Log::write('招就办收益冻结成功 zsb_id=' . $order['zsb_id'] . ', amount=' . $order['zsb_commission'] . '分（' . $zsbCommissionYuan . '元，不可提现）', 'INFO');
            }

            // 提交事务
            $serviceStationModel->commit();
            return true;

        } catch (\Exception $e) {
            // 回滚事务
            $serviceStationModel->rollback();
            \Think\Log::write('冻结招就办订单奖励失败: ' . $e->getMessage() . ', 订单ID: ' . $orderId, 'ERROR');
            return false;
        }
    }

    /**
     * 冻结普通订单奖励
     * @param int $orderId 订单ID
     * @param array $order 订单信息
     * @return bool 成功返回true，失败返回false
     */
    private function freezeNormalOrderReward($orderId, $order) {
        // 只处理直属服务站奖励，不处理上级奖励
        if ($order['reward_station_amt'] > 0) {
            try {
                // 开启事务
                $serviceStationModel = D('ServiceStation');
                $stationMoneyModel = D('StationMoney');

                $serviceStationModel->startTrans();

                // 使用行级锁更新服务站冻结余额
                $stationInfo = $serviceStationModel->where(['id' => $order['station_id']])->lock(true)->find();
                if (!$stationInfo) {
                    throw new \Exception('服务站信息不存在');
                }

                // 将分转换为元（z_service_station表存储的是元单位）
                $rewardStationAmtYuan = $order['reward_station_amt'] / 100;

                // 增加冻结余额和总金额
                $freezeResult = $serviceStationModel->where(['id' => $order['station_id']])
                    ->save([
                        'freeze_price' => ['exp', 'freeze_price + ' . $rewardStationAmtYuan],
                        'total_price' => ['exp', 'total_price + ' . $rewardStationAmtYuan]
                    ]);

                if ($freezeResult === false) {
                    throw new \Exception('更新冻结余额失败');
                }

                // 记录资金明细 - 培训奖励获得（z_station_money表存储的是元单位）
                $stationMoneyData = [
                    'service_station_id' => $order['station_id'],
                    'type' => 5, // 培训奖励(冻结)
                    'money' => $rewardStationAmtYuan, // 元单位
                    'create_time' => time(),
                    'remark' => '确认收款-培训奖励 ¥' . number_format($order['reward_station_amt'] / 100, 2) . ' 已添加到冻结余额 [订单:' . $orderId . ']',
                ];

                $moneyResult = $stationMoneyModel->add($stationMoneyData);
                if (!$moneyResult) {
                    throw new \Exception('记录资金明细失败');
                }

                // 提交事务
                $serviceStationModel->commit();
                return true;

            } catch (\Exception $e) {
                // 回滚事务
                $serviceStationModel->rollback();
                \Think\Log::write('冻结服务站奖励失败: ' . $e->getMessage() . ', 订单ID: ' . $orderId, 'ERROR');
                return false;
            }
        }

        return true;
    }

    /**
     * 解锁奖励
     * @param int $orderId 订单ID
     * @param string $completeType 完成类型：'employed'(已入职) 或 'terminated'(服务终止)
     * @return bool 成功返回true，失败返回false
     */
    private function unlockReward($orderId, $completeType = 'employed') {
        $orderModel = D("TrainingOrder");
        $order = $orderModel->where(['id' => $orderId])->find();
        if (!$order || $order['reward_status'] != 'locked') return false;

        // 检查是否为招就办订单
        if (!empty($order['zsb_id']) && $order['zsb_id'] > 0) {
            // 招就办订单：使用专门的解锁逻辑
            return $this->unlockZjbOrderReward($orderId, $order, $completeType);
        } else {
            // 普通订单：使用传统解锁逻辑
            return $this->unlockNormalOrderReward($orderId, $order, $completeType);
        }
    }

    /**
     * 解锁招就办订单奖励
     * @param int $orderId 订单ID
     * @param array $order 订单信息
     * @param string $completeType 完成类型
     * @return bool 成功返回true，失败返回false
     */
    private function unlockZjbOrderReward($orderId, $order, $completeType) {
        // 判断是否给奖励
        $shouldReward = ($completeType == 'employed');

        try {
            // 开启事务
            $serviceStationModel = D('ServiceStation');
            $stationMoneyModel = D('StationMoney');

            $serviceStationModel->startTrans();

            // 1. 处理服务站总收益（station_profit + zsb_commission）
            $stationTotalReward = $order['station_profit'] + $order['zsb_commission']; // 分单位
            if ($stationTotalReward > 0) {
                // 使用行级锁获取服务站信息
                $stationInfo = $serviceStationModel->where(['id' => $order['station_id']])->lock(true)->find();
                if (!$stationInfo) {
                    throw new \Exception('服务站信息不存在');
                }

                // 将分转换为元进行余额检查（z_service_station表存储的是元单位）
                $stationTotalRewardYuan = $stationTotalReward / 100;

                // 检查冻结余额是否充足
                if ($stationInfo['freeze_price'] < $stationTotalRewardYuan) {
                    throw new \Exception('服务站冻结余额不足，无法解锁奖励');
                }

                // 区分已入职和服务终止两种情况
                if ($shouldReward) {
                    // 已入职：从冻结余额转移到可提现余额
                    $transferResult = $serviceStationModel->where(['id' => $order['station_id']])
                        ->save([
                            'freeze_price' => ['exp', 'freeze_price - ' . $stationTotalRewardYuan],
                            'price' => ['exp', 'price + ' . $stationTotalRewardYuan]
                        ]);

                    if ($transferResult === false) {
                        throw new \Exception('服务站余额转移失败');
                    }

                    // 记录资金明细 - 培训奖励解冻（z_station_money表存储的是元单位）
                    $stationMoneyData = [
                        'service_station_id' => $order['station_id'],
                        'type' => 6, // 培训奖励(解冻)
                        'money' => $stationTotalReward / 100, // 元单位
                        'create_time' => time(),
                        'remark' => '培训完成-已入职-招就办奖励解冻 ¥' . number_format($stationTotalReward / 100, 2) . ' 从冻结余额转入可用余额（含招就办佣金） [订单:' . $orderId . ']',
                    ];
                } else {
                    // 服务终止：扣除冻结余额和总余额
                    $transferResult = $serviceStationModel->where(['id' => $order['station_id']])
                        ->save([
                            'freeze_price' => ['exp', 'freeze_price - ' . $stationTotalRewardYuan],
                            'total_price' => ['exp', 'total_price - ' . $stationTotalRewardYuan]
                        ]);

                    if ($transferResult === false) {
                        throw new \Exception('服务站冻结余额扣除失败');
                    }

                    // 记录资金明细 - 培训奖励扣除
                    $stationMoneyData = [
                        'service_station_id' => $order['station_id'],
                        'type' => 10, // 培训奖励(扣除)
                        'money' => $stationTotalReward / 100, // 存储绝对值，符号由type类型决定
                        'create_time' => time(),
                        'remark' => '培训完成-服务终止-招就办奖励扣除 ¥' . number_format($stationTotalReward / 100, 2) . ' 从冻结余额和总余额中扣除（含招就办佣金） [订单:' . $orderId . ']',
                    ];
                }

                $moneyResult = $stationMoneyModel->add($stationMoneyData);
                if (!$moneyResult) {
                    throw new \Exception('记录服务站资金明细失败');
                }

                \Think\Log::write('服务站总收益解锁成功 station_id=' . $order['station_id'] . ', total_amount=' . $stationTotalReward . '分, type=' . $completeType, 'INFO');
            }

            // 2. 处理招就办收益
            if ($order['zsb_commission'] > 0) {
                // 使用行级锁获取招就办信息
                $zsbInfo = $serviceStationModel->where(['id' => $order['zsb_id']])->lock(true)->find();
                if (!$zsbInfo) {
                    throw new \Exception('招就办信息不存在');
                }

                // 将分转换为元进行余额检查（z_service_station表存储的是元单位）
                $zsbCommissionYuan = $order['zsb_commission'] / 100;

                // 检查冻结余额是否充足
                if ($zsbInfo['freeze_price'] < $zsbCommissionYuan) {
                    throw new \Exception('招就办冻结余额不足，无法解锁奖励');
                }

                // 区分已入职和服务终止两种情况
                if ($shouldReward) {
                    // 已入职：招就办收益不可提现，保持在冻结状态（实际上是转为不可提现余额）
                    $zsbTransferResult = $serviceStationModel->where(['id' => $order['zsb_id']])
                        ->save([
                            'freeze_price' => ['exp', 'freeze_price - ' . $zsbCommissionYuan]
                            // 注意：招就办收益不增加到price（可提现余额），保持在total_price中作为统计
                        ]);

                    if ($zsbTransferResult === false) {
                        throw new \Exception('招就办余额转移失败');
                    }

                    // 记录资金明细 - 招就办奖励解冻（但不可提现）
                    $zsbMoneyData = [
                        'service_station_id' => $order['zsb_id'],
                        'type' => 12, // 招就办培训奖励(解冻但不可提现)
                        'money' => $order['zsb_commission'] / 100, // 元单位
                        'create_time' => time(),
                        'remark' => '培训完成-已入职-招就办佣金解冻 ¥' . number_format($order['zsb_commission'] / 100, 2) . ' 从冻结余额解冻（不可提现，仅统计） [订单:' . $orderId . ']',
                    ];
                } else {
                    // 服务终止：扣除冻结余额和总余额
                    $zsbTransferResult = $serviceStationModel->where(['id' => $order['zsb_id']])
                        ->save([
                            'freeze_price' => ['exp', 'freeze_price - ' . $zsbCommissionYuan],
                            'total_price' => ['exp', 'total_price - ' . $zsbCommissionYuan]
                        ]);

                    if ($zsbTransferResult === false) {
                        throw new \Exception('招就办冻结余额扣除失败');
                    }

                    // 记录资金明细 - 招就办奖励扣除（z_station_money表存储的是元单位）
                    $zsbMoneyData = [
                        'service_station_id' => $order['zsb_id'],
                        'type' => 13, // 招就办培训奖励(扣除)
                        'money' => -$order['zsb_commission'] / 100, // 使用负值表示扣除，元单位
                        'create_time' => time(),
                        'remark' => '培训完成-服务终止-招就办佣金扣除 ¥' . number_format($order['zsb_commission'] / 100, 2) . ' 从冻结余额和总余额中扣除 [订单:' . $orderId. ']',
                    ];
                }

                $zsbMoneyResult = $stationMoneyModel->add($zsbMoneyData);
                if (!$zsbMoneyResult) {
                    throw new \Exception('记录招就办资金明细失败');
                }

                \Think\Log::write('招就办收益解锁成功 zsb_id=' . $order['zsb_id'] . ', amount=' . $order['zsb_commission'] . '分, type=' . $completeType, 'INFO');
            }

            // 提交事务
            $serviceStationModel->commit();
            return true;

        } catch (\Exception $e) {
            // 回滚事务
            $serviceStationModel->rollback();
            \Think\Log::write('解锁招就办订单奖励失败: ' . $e->getMessage() . ', 订单ID: ' . $orderId, 'ERROR');
            return false;
        }
    }

    /**
     * 解锁普通订单奖励
     * @param int $orderId 订单ID
     * @param array $order 订单信息
     * @param string $completeType 完成类型
     * @return bool 成功返回true，失败返回false
     */
    private function unlockNormalOrderReward($orderId, $order, $completeType) {
        // 直属服务站奖励：根据完成类型决定是解冻还是扣除
        $shouldRewardStation = ($completeType == 'employed');

        // 上级服务站奖励功能已取消 - 强制禁用上级奖励发放
        $shouldRewardParent = false; // 原来是: ($completeType == 'employed');
        \Think\Log::write('上级服务站奖励功能已取消，强制设置shouldRewardParent=false: 订单ID=' . $orderId, 'INFO');

        try {
            // 开启事务
            $serviceStationModel = D('ServiceStation');
            $stationMoneyModel = D('StationMoney');

            $serviceStationModel->startTrans();

            // 处理直属服务站奖励
            if ($order['reward_station_amt'] > 0) {
                // 使用行级锁获取服务站信息
                $stationInfo = $serviceStationModel->where(['id' => $order['station_id']])->lock(true)->find();
                if (!$stationInfo) {
                    throw new \Exception('直属服务站信息不存在');
                }

                // 将分转换为元进行余额检查（z_service_station表存储的是元单位）
                $rewardStationAmtYuan = $order['reward_station_amt'] / 100;

                // 检查冻结余额是否充足
                if ($stationInfo['freeze_price'] < $rewardStationAmtYuan) {
                    throw new \Exception('冻结余额不足，无法解锁奖励');
                }

                // 区分已入职和服务终止两种情况
                if ($shouldRewardStation) {
                    // 已入职：从冻结余额转移到可提现余额
                    $transferResult = $serviceStationModel->where(['id' => $order['station_id']])
                        ->save([
                            'freeze_price' => ['exp', 'freeze_price - ' . $rewardStationAmtYuan],
                            'price' => ['exp', 'price + ' . $rewardStationAmtYuan]
                        ]);

                    if ($transferResult === false) {
                        throw new \Exception('直属服务站余额转移失败');
                    }

                    // 记录资金明细 - 培训奖励解冻（z_station_money表存储的是元单位）
                    $stationMoneyData = [
                        'service_station_id' => $order['station_id'],
                        'type' => 6, // 培训奖励(解冻)
                        'money' => $order['reward_station_amt'] / 100, // 元单位
                        'create_time' => time(),
                        'remark' => '培训完成-已入职-奖励解冻 ¥' . number_format($order['reward_station_amt'] / 100, 2) . ' 从冻结余额转入可用余额 [订单:' . $orderId . ']',
                    ];
                } else {
                    // 服务终止：扣除冻结余额和总余额
                    $transferResult = $serviceStationModel->where(['id' => $order['station_id']])
                        ->save([
                            'freeze_price' => ['exp', 'freeze_price - ' . $rewardStationAmtYuan],
                            'total_price' => ['exp', 'total_price - ' . $rewardStationAmtYuan]
                        ]);

                    if ($transferResult === false) {
                        throw new \Exception('直属服务站冻结余额扣除失败');
                    }

                    // 记录资金明细 - 培训奖励扣除
                    $stationMoneyData = [
                        'service_station_id' => $order['station_id'],
                        'type' => 10, // 培训奖励(扣除) - 使用新添加的类型10
                        'money' => $order['reward_station_amt'] / 100, // 存储绝对值，符号由type类型决定
                        'create_time' => time(),
                        'remark' => '培训完成-服务终止-奖励扣除 ¥' . number_format($order['reward_station_amt'] / 100, 2) . ' 从冻结余额和总余额中扣除 [订单:' . $orderId . ']',
                    ];
                }

                $moneyResult = $stationMoneyModel->add($stationMoneyData);
                if (!$moneyResult) {
                    throw new \Exception('记录直属服务站资金明细失败');
                }
            }

            // 处理上级服务站奖励 - 仅在"已入职"时发放
            if (false && $shouldRewardParent && $order['parent_station_id'] && $order['reward_parent_amt'] > 0) { // 上级奖励功能已取消
                // 使用行级锁获取上级服务站信息
                $parentStationInfo = $serviceStationModel->where(['id' => $order['parent_station_id']])->lock(true)->find();
                if (!$parentStationInfo) {
                    throw new \Exception('上级服务站信息不存在');
                }

                // 将分转换为元（z_service_station表存储的是元单位）
                $rewardParentAmtYuan = $order['reward_parent_amt'] / 100;

                // 直接增加上级服务站可提现余额和总金额
                $parentResult = $serviceStationModel->where(['id' => $order['parent_station_id']])
                    ->save([
                        'price' => ['exp', 'price + ' . $rewardParentAmtYuan],
                        'total_price' => ['exp', 'total_price + ' . $rewardParentAmtYuan]
                    ]);

                if ($parentResult === false) {
                    throw new \Exception('上级服务站余额更新失败');
                }

                // 记录资金明细 - 上级培训奖励（直接发放到可用余额）
                $parentMoneyData = [
                    'service_station_id' => $order['parent_station_id'],
                    'type' => 7, // 上级培训奖励
                    'money' => $rewardParentAmtYuan, // 元单位
                    'create_time' => time(),
                    'remark' => '下级培训完成-已入职-上级奖励 ¥' . number_format($rewardParentAmtYuan, 2) . ' 直接发放到可用余额 [订单:' . $orderId . ']',
                ];

                $parentMoneyResult = $stationMoneyModel->add($parentMoneyData);
                if (!$parentMoneyResult) {
                    throw new \Exception('记录上级服务站资金明细失败');
                }
            } elseif (!$shouldRewardParent && $order['parent_station_id'] && $order['reward_parent_amt'] > 0) {
                // 服务终止时，记录说明上级奖励未发放的原因
                \Think\Log::write('服务终止-上级奖励未发放: 订单ID=' . $orderId . ', 上级服务站ID=' . $order['parent_station_id'] . ', 未发放金额=' . $order['reward_parent_amt'], 'INFO');
            }

            // 提交事务
            $serviceStationModel->commit();
            return true;

        } catch (\Exception $e) {
            // 回滚事务
            $serviceStationModel->rollback();
            \Think\Log::write('解锁普通订单奖励失败: ' . $e->getMessage() . ', 订单ID: ' . $orderId . ', 完成类型: ' . $completeType, 'ERROR');
            return false;
        }
    }

    /**
     * 购买资源包
     */
    public function buy() {
        $id = I('get.service_station_id');
        $obja = D("ServiceStation");
        if (!$id) $this->error('请选择正确的服务站');
        $serviceStationlRow = $obja->where(['id' => $id])->find();
        if (!$serviceStationlRow) $this->error('当前服务站不存在');
        $this->assign('serviceStationlRow', $serviceStationlRow);
        $this->assign('buyList', $obja->buyList);

        if ($id) {
            $row = D("ServiceStation")->where("id=".$id)->find();
            if (!$row) $this->error('参数错误');
            $this->assign('row',$row);

            $rowp = D("ServiceStation")->where("id=".$row['pid'])->find();
            if (!$rowp) $this->error('参数错误');
            $this->assign('pidnum',$rowp['open_num']);

        } else {
            $this->error('参数错误');
        }
        $obj = D("service_station_buy_log"); //日志表写入
        if (IS_POST) {

            $p_buy_type = I('post.buy_type', 0);
            $p_buy_open_num = I('post.buy_open_num', 0);
            if ($p_buy_type == '3' && $p_buy_open_num > $rowp['open_num']) $this->error('上级推荐服务站资源包不足使用资源包抵扣方式');

            if ($data = $obj->create()) {


                $data['create_time'] = time();
                $data['service_station_id'] = $data['id'];
                unset($data['id']);
                $insertId = $obj->add($data); //写入购买日志表
               if ($insertId) { //添加日志然后服务站数量更新数量
                   D("ServiceStation")->save([
                        'id' => $row['id'],
                        'all_open_num' => ['exp', 'all_open_num+'.$data['buy_open_num']], //开通数量添加
                        'open_num' => ['exp', 'open_num+'.$data['buy_open_num']], //开通数量添加
                        'resources' => '1',
                    ]);
                    // 划拨
                    if ($p_buy_type == 3) {
                        $insertId = D("ServiceStationDivideLog")->add([
                            'user_id' => 0,
                            'service_station_id' => $rowp['id'],
                            'divide_service_station_id' => $row['id'],
                            'service_station_open_num' => $rowp['open_num'],
                            'divide_open_num' => $data['buy_open_num'],
                        ]);
                        D("ServiceStation")->save([
                            'id' => $rowp['id'],
                            'open_num' => ['exp', 'open_num-'.$data['buy_open_num']],
                            'divide_open_num' => ['exp', 'divide_open_num+'.$data['buy_open_num']]
                        ]);
                    }
                }
//                if (!$id) {
//                    $data['create_time'] = time();
//                    $insertId = $obj->add($data);
//                } else {
//                    $data['id'] = $id;
//                    $obj->save($data);
//                } servicestation/buylog/service_station_id/4
//$this->success("操作成功", U("servicestation/buylog", ["service_station_id" => $rowb['service_station_id']]));
                $this->success("操作成功", U("servicestation/buylog", ["service_station_id" => $row['id']]));
                exit;
            } else {
                $this->error($obj->getError());
                exit;
            }
        }
        $this->assign('usersList', D("Users")->where(['status' => 1, 'id' => ['neq', 1]])->getField('id,username', true));

        $this->display();
    }

    /**
     * 查看资源包购买日志/编辑
     */
    public function buylog() {
        $id = I('get.service_station_id');
        $obja = D("ServiceStation");
        if (!$id) $this->error('请选择正确的服务站');
        $serviceStationlRow = $obja->where(['id' => $id])->find();
        if (!$serviceStationlRow) $this->error('当前服务站不存在');
        $this->assign('serviceStationlRow', $serviceStationlRow);
        $this->assign('buyList', $obja->buyList);

        if ($id) {
            $row = D("ServiceStation")->where("id=".$id)->find();
            if (!$row) $this->error('参数错误');
            $this->assign('row',$row);

        } else {
            $this->error('参数错误');
        }
        $obj = D("service_station_buy_log");
        $where = [
            'service_station_id' => $id,
        ];
        $count = $obj->where($where)->count();
        $page = $this->page($count, 100);
        $sort_param = sortParam('id', 'desc');
        $list = $obj
            ->order("{$sort_param['field']} {$sort_param['order']}")
            ->limit($page->firstRow . ',' . $page->listRows)
            ->where($where)
            ->select();
        $userJobRow = D("UserJob")->where(['id' => $userJobId])->find();
        $this->assign('userJobRow', $userJobRow);
        $this->assign('list',$list);
        $this->display();
    }
 /**
     * 编辑日志购买资源包
     */
    public function editbuy() {

        $id =  I('get.id');
        $obj = D("service_station_buy_log");
        if (!$id) $this->error('无相关日志信息');
        $rowb = $obj->where(['id' => $id])->find();
        if (!$rowb) $this->error('当前日志不存在');
        $this->assign('rowb', $rowb);

        $obja = D("ServiceStation");
        $row = $obja->where("id=".$rowb['service_station_id'])->find();
        if ($row){
            $this->assign('row',$row);
           }
        $this->assign('buyList', $obja->buyList);

        if (IS_POST) {
            if ($data = $obj->create()) {
                if (!$id) {
                    $data['create_time'] = time();
                    $data['add_user'] = session('admin_name');
                    $insertId = $obj->add($data);
                    if ($insertId) {
                        D("ServiceStation")->save([
                            'id' => $rowb['service_station_id'],
                            'all_open_num' => ['exp', 'all_open_num+'.$data['buy_open_num']], //开通数量添加
                            'open_num' => ['exp', 'open_num+'.$data['buy_open_num']], //开通数量添加
                            'resources' => '1',
                        ]);
                    }
                } else {
                    $data['id'] = $id;
                    $data['last_edittime'] = time();
                    $data['edit_user'] = session('admin_name');
                    $obj->save($data);
                }
                $this->success("操作成功", U("servicestation/buylog", ["service_station_id" => $rowb['service_station_id']]));
                exit;
            } else {
                $this->error($obj->getError());
                exit;
            }
        }
        $this->assign('usersList', D("Users")->where(['status' => 1, 'id' => ['neq', 1]])->getField('id,username', true));
        $this->display();

    }

    /**
     * other 其他文件管理
     */
    public function other() {
        $id = I('get.service_station_id');
        if (!$id) $this->error('请选择正确的服务站');
        $serviceStationlRow = D("ServiceStation")->where(['id' => $id])->find();
        if (!$serviceStationlRow) $this->error('当前服务站不存在');
        $this->assign('serviceStationlRow', $serviceStationlRow);
        $c_kw = [
            'id' => 'ID',
            'name' => '名称',
        ];
        $where = ['service_station_id' => $serviceStationlRow['id']];

        $obj = D("ServiceStationFile");
        $count = $obj->where($where)->count();
        $page = $this->page($count, 100);
        $sort_param = sortParam('id', 'desc');
        $list = $obj
            ->order("{$sort_param['field']} {$sort_param['order']}")
            ->limit($page->firstRow . ',' . $page->listRows)
            ->where($where)
            ->select();
        $this->assign('_get', I('get.'));
        $this->assign('list',$list);
        $this->assign("page", $page->show());
        $this->assign('statusList', D("Project")->status);
        $this->assign('c_kw', $c_kw);
        $this->display();
    }

    public function otheredit() {
        $ids = I('get.service_station_id');
        if (!$ids) $this->error('请选择正确的服务站');
        $serviceStationlRow = D("ServiceStation")->where(['id' => $ids])->find();
        if (!$serviceStationlRow) $this->error('当前服务站不存在');
        $id = I('get.id', 0);
        $obj = D("ServiceStationFile");
        if ($id) {
            $row = $obj->where("id=".$id)->find();
            if (!$row) $this->error('参数错误');
            $this->assign('row',$row);
        }
        $obj = D("ServiceStationFile");
        if (IS_POST) {
            if ($data = $obj->create()) {
                if (!$id) {
                    $data['create_time'] = time();
                    $data['service_station_id'] = $ids;
                    $insertId = $obj->add($data);
                } else {
                    $data['id'] = $id;
                    $obj->save($data);
                }
                $this->success("操作成功", U("servicestation/other", ['service_station_id' => $ids]));
                exit;
            } else {
                $this->error($obj->getError());
                exit;
            }
        }
        $this->display();
    }

    /**
     * 状态变更
     */
    public function cgstat() {
        $id = I('get.id');
        $status = I('get.status');
        if (!$id) $this->error('参数错误');
        $obj = D("ServiceStation");
        $row = $obj->where(['id' => $id])->find();
        if ($row['status'] == 1) $this->error('当前服务站已经审核通过');
        if (!in_array($status, [0,1,2])) $this->error('参数错误 ');
        if ($status == 1) {
            $pidRows = [];
            $pidTop = false;
            if ($row['pid'] > 0) {
                $pidRows = $obj->where(['id' => $row['pid']])->find();
            } else {
                $pidTop = true;
            }
            if (($row['pid'] > 0 && $pidRows && ($pidRows['open_num']- $pidRows['succ_open_num']) > 0) || $pidTop) {
                //服务站
                if (!D("Qrcode")->where(['service_id' => 2, 'service_station_id' => $row['id']])->find()) {
                    $code1 = D("Qrcode")->createNew(2, 1, $row['id']);
                } else {
                    $code1 = true;
                }
                //服务平台
                if (!D("Qrcode")->where(['service_id' => 1, 'service_station_id' => $row['id']])->find()) {
                    $code = D("Qrcode")->createNew(1, 1, $row['id']);
                } else {
                    $code = true;
                }
                if ($code &&  $code1) {
                    $obj->save(['id' => $id,'open_type' =>'1', 'status' => $status]);
                    D("User")->where(['self_service_station_id' => $row['id']])->save(['is_service_station' => 2]);
                    if ($pidRows) {
                        $obj->where(['id' => $pidRows['id']])->save(['succ_open_num' => ['exp', 'succ_open_num+1'],'open_num' => ['exp', 'open_num-1']]);
                    }

                    $alisms = new AliSms();
                    $result = $alisms->sendSmsstate($row['contract_name'],'通过', $row['mobile']);


                    $this->success('修改成功');
                } else {
                    $this->error('开通失败，请联系技术处理');
                }
            } else {
                $this->error('当前服务站的开通数量不足');
            }
        } else {
            $obj->save(['id' => $id, 'status' => $status]);
        }
        $this->success('修改成功');
    }

    /**
     * 推荐方式开通服务站
     */
    public function tjstat() {
        $id = I('get.id');
        $status = I('get.status');
        if (!$id) $this->error('参数错误');
        $obj = D("ServiceStation");
        $row = $obj->where(['id' => $id])->find();
        if ($row['status'] == 1) $this->error('当前服务站已经审核通过');
        if (!in_array($status, [0,1,2])) $this->error('参数错误 ');
        if ($status == 1) {
            $pidRows = [];
            $pidTop = false;
            if ($row['pid'] > 0) {
                $pidRows = $obj->where(['id' => $row['pid']])->find();
            } else {
                $pidTop = true;
            }
            if (($row['pid'] > 0 && $pidRows) || $pidTop) {
                //服务站
                if (!D("Qrcode")->where(['service_id' => 2, 'service_station_id' => $row['id']])->find()) {
                    $code1 = D("Qrcode")->createNew(2, 1, $row['id']);
                } else {
                    $code1 = true;
                }
                //服务平台
                if (!D("Qrcode")->where(['service_id' => 1, 'service_station_id' => $row['id']])->find()) {
                    $code = D("Qrcode")->createNew(1, 1, $row['id']);
                } else {
                    $code = true;
                }
                if ($code &&  $code1) {
                    $obj->save(['id' => $id, 'status' => $status]);
                    D("User")->where(['self_service_station_id' => $row['id']])->save(['is_service_station' => 2]);
                    if ($pidRows) {
                        $obj->where(['id' => $pidRows['id']])->save(['succ_tj_open_num' => ['exp', 'succ_tj_open_num+1']]);
                    }

                    $alisms = new AliSms();
                    $result = $alisms->sendSmsstate($row['contract_name'],'通过', $row['mobile']);


                    $this->success('修改成功');
                } else {
                    $this->error('开通失败，请联系技术处理');
                }
            }
        } else {
            $obj->save(['id' => $id, 'status' => $status]);
        }
        $this->success('修改成功');
    }

    /**
     * 状态变更
     */
    public function othercgstat() {
        $id = I('get.id');
        $status = I('get.status');
        if (!$id) $this->error('参数错误');
        $obj = D("ServiceStationFile");
        if (!in_array($status, [0,1])) $this->error('参数错误 ');
        $obj->save(['id' => $id, 'status' => $status]);
        $this->success('修改成功');
    }

    /**
     * 服务站订单统一查看功能
     * 服务站可以查看招就办订单和自有订单，但都只有查看权限
     */
    public function stationOrdersView()
    {
        $stationId = I('get.station_id', 0, 'intval');
        if (!$stationId) {
            $this->error('请选择服务站');
        }

        // 验证服务站是否存在
        $station = D('ServiceStation')->where(['id' => $stationId, 'status' => 1])->find();
        if (!$station) {
            $this->error('服务站不存在或已禁用');
        }

        // 获取订单类型筛选参数
        $orderType = I('get.order_type', 'all'); // all, zsb, station
        $s_order_status = I("get.order_status");
        $s_payment_status = I("get.payment_status");
        $s_kwd = I("get.kwd");

        // 构建基础查询条件
        $where = [];

        // 根据订单类型设置查询条件
        switch ($orderType) {
            case 'zsb':
                // 招就办订单：zsb_id > 0 且招就办属于当前服务站
                $zsbIds = D('ServiceStation')->where([
                    'zsb_ref_station' => $stationId,
                    'zsb_type' => 2,
                    'status' => 1
                ])->getField('id', true);

                if (empty($zsbIds)) {
                    $where['zsb_id'] = 0; // 确保没有结果
                } else {
                    $where['zsb_id'] = ['in', $zsbIds];
                }
                break;
            case 'station':
                // 自有订单：station_id = 当前服务站 且 zsb_id = 0
                $where['station_id'] = $stationId;
                $where['zsb_id'] = 0;
                break;
            default:
                // 全部订单：招就办订单 + 自有订单
                $zsbIds = D('ServiceStation')->where([
                    'zsb_ref_station' => $stationId,
                    'zsb_type' => 2,
                    'status' => 1
                ])->getField('id', true);

                if (empty($zsbIds)) {
                    // 只有自有订单
                    $where['station_id'] = $stationId;
                    $where['zsb_id'] = 0;
                } else {
                    // 招就办订单 + 自有订单
                    $where['_complex'] = [
                        '_logic' => 'OR',
                        ['zsb_id' => ['in', $zsbIds]], // 招就办订单
                        ['station_id' => $stationId, 'zsb_id' => 0] // 自有订单
                    ];
                }
                break;
        }

        // 其他筛选条件
        if ($s_order_status != '') $where['order_status'] = $s_order_status;
        if ($s_payment_status != '') $where['payment_status'] = $s_payment_status;
        if ($s_kwd != '') {
            // 简单的关键词搜索，可以搜索订单ID
            if (is_numeric($s_kwd)) {
                $where['id'] = $s_kwd;
            }
        }

        // 自动隐藏服务站id为4的订单
        // 需要处理复杂查询条件的情况
        if (isset($where['_complex'])) {
            // 如果已经有复杂查询条件，需要在每个子条件中添加过滤
            foreach ($where['_complex'] as $key => &$condition) {
                if ($key !== '_logic' && is_array($condition)) {
                    $condition['station_id'] = ['neq', 4];
                }
            }
        } else {
            // 简单查询条件，直接添加过滤
            if (isset($where['station_id']) && is_array($where['station_id'])) {
                // 如果station_id已经是数组条件，需要合并
                $where['_complex'] = [
                    '_logic' => 'AND',
                    $where,
                    ['station_id' => ['neq', 4]]
                ];
                unset($where['station_id']);
            } else {
                $where['station_id'] = ['neq', 4];
            }
        }

        // 分页
        $pageSize = 20;
        $obj = D("TrainingOrder");
        $count = $obj->where($where)->count();
        $page = $this->page($count, $pageSize);

        // 获取订单列表
        $list = $obj->getOrderList($where, 'create_time desc', $page->firstRow, $page->listRows);

        // 注释：改为依赖表数据，移除实时计算逻辑
        // 招就办订单的收益信息直接使用数据库存储的字段
        foreach ($list as &$item) {
            if (!empty($item['is_zsb_order']) && $item['is_zsb_order']) {
                // 使用数据库存储的收益字段（分单位转元单位）
                $item['station_profit_yuan'] = round(($item['station_profit'] + $item['zsb_commission']) / 100, 2);

                // 设置详细收益信息（直接使用存储的字段）
                $item['station_profit_detail'] = $item['station_profit']; // 服务站收益（分）
                $item['zsb_commission_detail'] = $item['zsb_commission']; // 招就办收益（分）
            }
        }
        unset($item); // 清除引用

        // 获取服务站下的招就办列表
        $zsbList = D('ServiceStation')->where([
            'zsb_ref_station' => $stationId,
            'zsb_type' => 2,
            'status' => 1
        ])->field('id, service_name')->select();

        // 模板赋值
        $this->assign('station', $station);
        $this->assign('zsbList', $zsbList);
        $this->assign('orderType', $orderType);
        $this->assign('order_status_list', $obj->order_status);
        $this->assign('payment_status_list', $obj->payment_status);
        $this->assign('list', $list);
        $this->assign('pageSize', $pageSize);
        $this->assign("page", $page->show());
        $this->assign('_get', I('get.'));
        $this->assign('viewOnly', true); // 标记为纯查看模式

        $this->display();
    }

    /**
     * 服务站订单详情查看（只读）
     */
    public function stationOrderDetail()
    {
        $id = intval(I('get.id'));
        $stationId = I('get.station_id', 0, 'intval');

        if (!$id || !$stationId) {
            $this->error('参数错误');
        }

        // 验证服务站权限
        $station = D('ServiceStation')->where(['id' => $stationId, 'status' => 1])->find();
        if (!$station) {
            $this->error('服务站不存在或已禁用');
        }

        // 获取订单信息并验证权限
        $orderModel = D("TrainingOrder");
        $order = $orderModel->where(['id' => $id])->find();
        if (!$order) {
            $this->error('订单不存在');
        }

        // 验证服务站是否有权限查看此订单
        $hasPermission = false;
        if ($order['station_id'] == $stationId && empty($order['zsb_id'])) {
            // 自有订单
            $hasPermission = true;
        } elseif (!empty($order['zsb_id'])) {
            // 招就办订单，检查招就办是否属于当前服务站
            $zsb = D('ServiceStation')->where([
                'id' => $order['zsb_id'],
                'zsb_ref_station' => $stationId,
                'zsb_type' => 2
            ])->find();
            if ($zsb) {
                $hasPermission = true;
            }
        }

        if (!$hasPermission) {
            $this->error('无权查看此订单');
        }

        // 获取关联信息（复用detail方法的逻辑）
        $user = D('User')->where(['id' => $order['user_id']])->find();

        // 优先使用简历信息
        $userJob = null;
        if (!empty($order['user_job_id'])) {
            $userJob = D('UserJob')->where(['id' => $order['user_job_id']])->find();
        }

        if ($userJob) {
            $user['display_realname'] = $userJob['name'];
            $user['display_mobile'] = $userJob['phone'];
            $user['idcard'] = $userJob['id_number'];
            $user['from_resume'] = true;
        } else {
            $user['display_realname'] = $user['nickname'];
            $user['display_mobile'] = $user['mobile'];
            $user['from_resume'] = false;
        }

        $post = D('ProjectPost')->where(['id' => $order['post_id']])->find();
        $project = D('Project')->where(['id' => $post['project_id']])->find();
        $orderStation = D('ServiceStation')->where(['id' => $order['station_id']])->find();

        // 如果是招就办订单，获取招就办信息
        $zsb = null;
        if (!empty($order['zsb_id'])) {
            $zsb = D('ServiceStation')->where(['id' => $order['zsb_id']])->find();
        }

        // 获取支付记录
        $paymentRecords = D('PaymentRecord')->getRecordsByOrderId($id);

        // 模板赋值
        $this->assign('order', $order);
        $this->assign('user', $user);
        $this->assign('post', $post);
        $this->assign('project', $project);
        $this->assign('station', $station);
        $this->assign('orderStation', $orderStation);
        $this->assign('zsb', $zsb);
        $this->assign('payment_records', $paymentRecords);
        $this->assign('order_status', $orderModel->order_status);
        $this->assign('payment_status', $orderModel->payment_status);
        $this->assign('reward_status', $orderModel->reward_status);
        $this->assign('viewOnly', true); // 标记为纯查看模式

        $this->display();
    }

    /**
     * 计算招就办订单的详细收益信息
     * @param array &$order 订单信息（引用传递）
     * @deprecated 已废弃，改为直接使用数据库存储的收益字段
     */
    private function calculateZjbOrderRewards(&$order)
    {
        // 注释：此方法已废弃，改为直接使用数据库存储的收益字段
        // 直接使用数据库中的 station_profit 和 zsb_commission 字段
        if (empty($order['zsb_id']) || $order['zsb_id'] <= 0) {
            return;
        }

        // 设置详细收益信息（直接使用存储的字段）
        $order['station_profit_detail'] = !empty($order['station_profit']) ? $order['station_profit'] : 0;
        $order['zsb_commission_detail'] = !empty($order['zsb_commission']) ? $order['zsb_commission'] : 0;
    }

    /**
     * 获取平台费率
     * @return float 平台费率
     */
    private function getPlatformRate() {
        $conf = D('Conf')->where(['name' => 'platform_rate'])->find();
        return $conf ? floatval($conf['value']) : 0.2; // 默认20%
    }

    /**
     * 状态更新接口（新的二级状态系统）
     */
    public function updateStatus()
    {
        $orderId = I('post.order_id');
        $newStatus = I('post.status');
        $remark = I('post.remark', '');
        $amountInfo = I('post.amount_info', []);
        $payChannel = I('post.pay_channel', 'manual');

        if (empty($orderId) || empty($newStatus)) {
            $this->ajaxReturn(['status' => 0, 'info' => '参数错误']);
        }

        // 权限验证：只有管理员可以操作状态
        if (!session('admin_id')) {
            $this->ajaxReturn(['status' => 0, 'info' => '只有管理员可以操作订单状态']);
        }

        // 验证订单访问权限
        $orderModel = D('TrainingOrder');
        $order = $orderModel->where(['id' => $orderId])->find();
        if (!$order) {
            $this->ajaxReturn(['status' => 0, 'info' => '订单不存在']);
        }

        // 解析状态（支持原有状态和新状态）
        if (strpos($newStatus, '.') !== false) {
            // 新格式：main_status.sub_status
            list($mainStatus, $subStatus) = explode('.', $newStatus, 2);
        } else {
            // 兼容原有格式
            $result = $orderModel->updateOrderStatus($orderId, $newStatus);
            if ($result) {
                $this->ajaxReturn(['status' => 1, 'info' => '状态更新成功']);
            } else {
                $this->ajaxReturn(['status' => 0, 'info' => '状态更新失败']);
            }
            return;
        }

        // 检查是否跳过验证（仅超级管理员）
        $skipValidation = I('post.skip_validation', 0) && session('admin_id') == 1;

        // 使用新的状态更新方法（管理员操作）
        $result = $orderModel->updateOrderStatusV2(
            $orderId,
            $mainStatus,
            $subStatus,
            $amountInfo,
            session('admin_id'),
            $remark,
            $payChannel,
            $skipValidation
        );

        // 处理不同类型的返回结果
        if (is_array($result)) {
            // 特殊状态：需要付款确认或资金操作确认
            $this->ajaxReturn($result);
        } elseif ($result) {
            $this->ajaxReturn(['status' => 1, 'info' => '状态更新成功']);
        } else {
            $this->ajaxReturn(['status' => 0, 'info' => '状态更新失败']);
        }
    }

    /**
     * 确认剩余款项收款并执行状态跳转（新功能）
     */
    public function confirmRemainingPayment()
    {
        if (!IS_POST) {
            $this->ajaxReturn(['status' => 0, 'info' => '请求方式错误']);
        }

        $orderId = I('post.order_id', 0, 'intval');
        $payChannel = I('post.pay_channel', 'offline');
        $targetStatus = I('post.target_status', '');
        $remark = I('post.remark', '');
        $remainingAmount = I('post.remaining_amount', 0, 'intval');

        if (!$orderId || !$targetStatus) {
            $this->ajaxReturn(['status' => 0, 'info' => '参数错误']);
        }

        $orderModel = D('TrainingOrder');

        // 验证订单存在
        $order = $orderModel->where(['id' => $orderId])->find();
        if (!$order) {
            $this->ajaxReturn(['status' => 0, 'info' => '订单不存在']);
        }

        // 解析目标状态
        if (strpos($targetStatus, '.') !== false) {
            list($mainStatus, $subStatus) = explode('.', $targetStatus, 2);
        } else {
            $this->ajaxReturn(['status' => 0, 'info' => '状态格式错误']);
        }

        try {
            // 开启事务
            $orderModel->startTrans();

            // 创建支付记录（使用剩余应付金额）
            if ($remainingAmount > 0) {
                $payData = [
                    'order_id' => $orderId,
                    'pay_channel' => $payChannel,
                    'pay_amount' => $remainingAmount,
                    'pay_type' => 'full', // 设置为全额付款
                    'pay_time' => time(),
                    'remark' => '确认收款 - 剩余金额：' . number_format($remainingAmount / 100, 2) . '元'
                ];

                $paymentModel = D('PaymentRecord');
                $payId = $paymentModel->createRecord($payData);

                if (!$payId) {
                    throw new \Exception('创建支付记录失败');
                }
            }

            // 执行状态跳转（跳过支付验证）
            $result = $orderModel->updateOrderStatusV2(
                $orderId,
                $mainStatus,
                $subStatus,
                [], // 不传递金额信息，保持订单原有报名费不变
                session('admin_id'),
                $remark ?: '确认收款',
                $payChannel,
                true // 跳过支付验证
            );

            if (!$result || (is_array($result) && $result['status'] !== 1)) {
                throw new \Exception('更新订单状态失败');
            }

            // 重新获取订单信息（包含最新的奖励计算结果）
            $order = $orderModel->where(['id' => $orderId])->find();
            if (!$order) {
                throw new \Exception('订单信息获取失败');
            }

            // 检查是否需要冻结资金
            // 当订单从"已全额收款"状态跳转到后续状态时，需要冻结相应的资金
            if ($this->shouldFreezeReward($order, $targetStatus)) {
                // 计算奖励金额（如果还没有计算）
                if (empty($order['reward_station_amt']) && empty($order['station_profit'])) {
                    $this->calculateRewardAndGetResults($orderId);
                    // 重新获取订单信息
                    $order = $orderModel->where(['id' => $orderId])->find();
                }

                // 冻结资金（根据订单类型选择不同的冻结逻辑）
                if (!empty($order['zsb_id']) && $order['zsb_id'] > 0) {
                    // 招就办订单：冻结服务站总收益（station_profit + zsb_commission）
                    $this->freezeStationReward($orderId);
                } else {
                    // 普通订单：冻结服务站奖励
                    $this->freezeStationReward($orderId);
                }

                \Think\Log::write('confirmRemainingPayment: 资金冻结完成 order_id=' . $orderId . ' target_status=' . $targetStatus, 'INFO');
            }

            $orderModel->commit();
            $this->ajaxReturn(['status' => 1, 'info' => '确认收款成功']);

        } catch (\Exception $e) {
            $orderModel->rollback();
            \Think\Log::write('确认收款失败: ' . $e->getMessage(), 'ERROR');
            $this->ajaxReturn(['status' => 0, 'info' => '确认收款失败：' . $e->getMessage()]);
        }
    }

    /**
     * 判断是否需要冻结资金
     * @param array $order 订单信息
     * @param string $targetStatus 目标状态
     * @return bool
     */
    private function shouldFreezeReward($order, $targetStatus)
    {
        // 解析目标状态
        if (strpos($targetStatus, '.') !== false) {
            list($mainStatus, $subStatus) = explode('.', $targetStatus, 2);
        } else {
            // 兼容旧格式
            $mainStatus = '';
            $subStatus = $targetStatus;
        }

        // 当前订单状态
        $currentMainStatus = $order['main_status'] ?? '';
        $currentSubStatus = $order['sub_status'] ?? '';

        // 检查是否从"已全额收款"状态跳转到后续状态
        $isFromFullPaid = ($currentSubStatus === 'full_paid');

        // 需要冻结资金的目标状态列表（基于实际的sub_status定义）
        $freezeRequiredStatuses = [
            'pending_notice',       // 待通知培训
            'notice_sent',          // 已通知培训
            'docs_signed',          // 已签署文件
            'submitted_institute',  // 已提交至培训机构
            'in_training',          // 参与培训中
            'waiting_notice',       // 等待入职通知
            'employed',             // 已入职
            'confirming',           // 服务完成确认中
            'completed',            // 服务完成
            'terminated'            // 服务终止
        ];

        // 如果从"已全额收款"状态跳转到需要冻结资金的状态，则需要冻结
        if ($isFromFullPaid && in_array($subStatus, $freezeRequiredStatuses)) {
            \Think\Log::write('shouldFreezeReward: 需要冻结资金 from=' . $currentSubStatus . ' to=' . $subStatus . ' order_id=' . $order['id'], 'INFO');
            return true;
        }

        // 特殊情况：如果订单已经有奖励金额但还没有冻结记录，也需要冻结
        if (($order['reward_station_amt'] > 0 || $order['station_profit'] > 0) && !$this->hasFreezedReward($order['id'])) {
            \Think\Log::write('shouldFreezeReward: 订单有奖励但未冻结 order_id=' . $order['id'], 'INFO');
            return true;
        }

        return false;
    }

    /**
     * 检查订单是否已经冻结过资金
     * @param int $orderId 订单ID
     * @return bool
     */
    private function hasFreezedReward($orderId)
    {
        // 检查z_station_money表中是否有冻结记录
        $stationMoneyModel = D('StationMoney');
        $freezeRecord = $stationMoneyModel->where([
            'remark' => ['like', '%订单:' . $orderId . '%'],
            'type' => ['in', [5, 13]] // 5=培训奖励(冻结), 13=招就办佣金(冻结)
        ])->find();

        return !empty($freezeRecord);
    }

    /**
     * 确认资金操作并执行状态跳转
     */
    public function confirmFundOperation()
    {
        if (!IS_POST) {
            $this->ajaxReturn(['status' => 0, 'info' => '请求方式错误']);
        }

        $orderId = I('post.order_id', 0, 'intval');
        $targetStatus = I('post.target_status', '');
        $remark = I('post.remark', '');

        if (!$orderId || !$targetStatus) {
            $this->ajaxReturn(['status' => 0, 'info' => '参数错误']);
        }

        // 解析目标状态
        if (strpos($targetStatus, '.') !== false) {
            list($mainStatus, $subStatus) = explode('.', $targetStatus, 2);
        } else {
            $this->ajaxReturn(['status' => 0, 'info' => '状态格式错误']);
        }

        $orderModel = D('TrainingOrder');

        // 执行状态跳转（跳过验证）
        $result = $orderModel->updateOrderStatusV2(
            $orderId,
            $mainStatus,
            $subStatus,
            [],
            session('admin_id'),
            $remark ?: '确认资金操作',
            'manual',
            true // 跳过验证
        );

        if ($result === true) {
            $this->ajaxReturn(['status' => 1, 'info' => '操作成功']);
        } else {
            $this->ajaxReturn(['status' => 0, 'info' => '操作失败']);
        }
    }

    /**
     * 获取支付历史记录
     */
    public function getPaymentHistory()
    {
        $orderId = I('get.order_id', 0, 'intval');
        if (!$orderId) {
            $this->ajaxReturn(['status' => 0, 'info' => '参数错误']);
        }

        $paymentModel = D('PaymentRecord');
        $payments = $paymentModel->where(['order_id' => $orderId])
            ->order('pay_time DESC')
            ->select();

        if ($payments) {
            foreach ($payments as &$payment) {
                // 格式化支付时间
                $payment['pay_time_text'] = date('Y-m-d H:i:s', $payment['pay_time']);

                // 支付类型文本
                $payTypeMap = [
                    'intent' => '意向金',
                    'partial' => '部分付款',
                    'full' => '全额付款',
                    'refund' => '退款'
                ];
                $payment['pay_type_text'] = $payTypeMap[$payment['pay_type']] ?? $payment['pay_type'];

                // 支付方式文本
                $payChannelMap = [
                    'offline' => '线下支付',
                    'alipay' => '支付宝',
                    'wechat' => '微信支付',
                    'bank' => '银行转账',
                    'free' => '公益免费',
                    'manual' => '手动录入'
                ];
                $payment['pay_channel_text'] = $payChannelMap[$payment['pay_channel']] ?? $payment['pay_channel'];
            }
        }

        $this->ajaxReturn([
            'status' => 1,
            'data' => $payments ?: []
        ]);
    }

    /**
     * 获取状态转换选项
     */
    public function getStatusOptions()
    {
        $orderId = I('get.order_id');
        if (empty($orderId)) {
            $this->ajaxReturn(['status' => 0, 'info' => '参数错误']);
        }

        $orderModel = D('TrainingOrder');
        $order = $orderModel->where(['id' => $orderId])->find();
        if (!$order) {
            $this->ajaxReturn(['status' => 0, 'info' => '订单不存在']);
        }

        $currentSubStatus = $order['sub_status'] ?? 'initial_contact';
        $statusConfig = $orderModel->sub_status[$currentSubStatus] ?? [];

        // 如果当前状态是最终状态，不允许再变更
        if ($statusConfig['is_final'] ?? false) {
            $this->ajaxReturn([
                'status' => 1,
                'data' => [],
                'order_info' => $order,
                'paid_amount' => 0,
                'remaining_amount' => 0,
                'info' => '当前状态为最终状态，无法变更'
            ]);
            return;
        }

        // 定义状态优先级顺序（数字越大表示越靠后的状态）
        $statusPriority = [
            // 沟通流程
            'initial_contact' => 10,
            'intent_paid' => 20,
            'contract_signed' => 30,
            'partial_paid' => 40,
            'full_paid' => 50,

            // 培训流程
            'pending_notice' => 60,
            'notice_sent' => 70,
            'docs_signed' => 80,
            'submitted_institute' => 90,
            'in_training' => 100,
            'end_training' => 105,

            // 入职流程
            'waiting_examination' => 110,
            'note_examination' => 115,
            'in_examinationloyed' => 120,
            'waiting_interview' => 125,
            'note_interview' => 130,
            'int_interviewyed' => 135,

            'waiting_notice' => 140,
            'employed' => 150,

            // 服务审查
            'confirming' => 130,
            'completed' => 140,
            'terminated' => 140, // 与completed同级，都是最终状态
        ];

        $currentPriority = $statusPriority[$currentSubStatus] ?? 0;

        // 只显示比当前状态优先级更高（更靠后）的状态
        $options = [];
        foreach ($orderModel->sub_status as $statusKey => $statusInfo) {
            $statusPriorityValue = $statusPriority[$statusKey] ?? 0;

            // 只显示优先级更高的状态
            if ($statusPriorityValue > $currentPriority) {
                $options[] = [
                    'value' => $statusInfo['main'] . '.' . $statusKey,
                    'text' => $statusInfo['text'],
                    'desc' => $statusInfo['desc'] ?? '',
                    'has_amount' => $statusInfo['has_amount'] ?? false,
                    'amount_field' => $statusInfo['amount_field'] ?? ''
                ];
            }
        }

        // 计算已支付金额
        $paymentModel = D('PaymentRecord');
        $paidAmount = $paymentModel->where(['order_id' => $orderId])
            ->sum('pay_amount'); // 分单位

        // 确保已支付金额不超过订单金额
        $paidAmount = $paidAmount ?: 0;
        if ($paidAmount > $order['fee_amount']) {
            $paidAmount = $order['fee_amount'];
        }

        // 计算剩余金额
        $remainingAmount = $order['fee_amount'] - $paidAmount;
        if ($remainingAmount < 0) {
            $remainingAmount = 0;
        }

        // 订单费用信息
        $orderInfo = [
            'fee_amount' => $order['fee_amount'], // 分单位
            'fee_amount_yuan' => $order['fee_amount'] / 100, // 元单位
            'paid_amount' => $paidAmount, // 分单位
            'paid_amount_yuan' => $paidAmount / 100, // 元单位
            'remaining_amount' => $remainingAmount, // 分单位
            'remaining_amount_yuan' => $remainingAmount / 100 // 元单位
        ];

        $this->ajaxReturn([
            'status' => 1,
            'data' => $options,
            'order_info' => $orderInfo
        ]);
    }

    /**
     * 获取订单状态历史
     */
    public function getStatusHistory()
    {
        $orderId = I('get.order_id');
        if (empty($orderId)) {
            $this->ajaxReturn(['status' => 0, 'info' => '参数错误']);
        }

        $orderModel = D('TrainingOrder');
        $order = $orderModel->where(['id' => $orderId])->find();
        if (!$order) {
            $this->ajaxReturn(['status' => 0, 'info' => '订单不存在']);
        }

        // 获取状态变更历史
        $statusHistoryModel = M('training_order_status_history');
        $statusHistory = $statusHistoryModel->where(['order_id' => $orderId])
            ->order('create_time desc')
            ->select();

        // 如果没有历史记录，为现有订单创建初始记录
        if (empty($statusHistory)) {
            $orderModel->createInitialStatusHistory($orderId);
            // 重新获取历史记录
            $statusHistory = $statusHistoryModel->where(['order_id' => $orderId])
                ->order('create_time desc')
                ->select();
        }

        // 处理状态历史数据
        $processedHistory = [];
        foreach ($statusHistory as $record) {
            $item = [
                'id' => $record['id'],
                'from_status' => [
                    'main' => $record['from_main_status'],
                    'sub' => $record['from_sub_status'],
                    'main_text' => $record['from_main_status'] ? ($orderModel->main_status[$record['from_main_status']]['text'] ?? $record['from_main_status']) : '',
                    'sub_text' => $record['from_sub_status'] ? ($orderModel->sub_status[$record['from_sub_status']]['text'] ?? $record['from_sub_status']) : ''
                ],
                'to_status' => [
                    'main' => $record['to_main_status'],
                    'sub' => $record['to_sub_status'],
                    'main_text' => $orderModel->main_status[$record['to_main_status']]['text'] ?? $record['to_main_status'],
                    'sub_text' => $orderModel->sub_status[$record['to_sub_status']]['text'] ?? $record['to_sub_status']
                ],
                'status_desc' => $record['status_desc'],
                'amount_info' => $record['amount_info'] ? json_decode($record['amount_info'], true) : null,
                'operator' => [
                    'id' => $record['operator_id'],
                    'type' => $record['operator_type'],
                    'name' => $record['operator_name']
                ],
                'remark' => $record['remark'],
                'create_time' => $record['create_time'],
                'create_time_text' => date('Y-m-d H:i:s', $record['create_time'])
            ];

            // 根据用户权限决定是否显示敏感信息
            if (session('admin_id')) {
                // 管理员可以看到完整信息
                $item['ip_address'] = $record['ip_address'];
                $item['user_agent'] = $record['user_agent'];
            }

            $processedHistory[] = $item;
        }

        // 获取付款记录
        $paymentRecordModel = D('PaymentRecord');
        $paymentRecords = $paymentRecordModel->where(['order_id' => $orderId])->order('create_time desc')->select();

        // 处理付款记录数据
        $processedPayments = [];
        $paymentTypes = [
            'intent' => '意向金',
            'partial' => '部分付款',
            'full' => '全额付款',
            'refund' => '退款'
        ];

        foreach ($paymentRecords as $payment) {
            // 使用PaymentRecordModel的转换逻辑获取渠道显示文本
            $channelText = isset($paymentRecordModel->pay_channel[$payment['pay_channel']]) ?
                $paymentRecordModel->pay_channel[$payment['pay_channel']] : $payment['pay_channel'];

            $processedPayments[] = [
                'id' => $payment['id'],
                'type' => $payment['pay_type'],
                'type_text' => $paymentTypes[$payment['pay_type']] ?? $payment['pay_type'],
                'channel' => $channelText, // 使用转换后的用户友好文本
                'amount' => $payment['pay_amount'],
                'amount_text' => number_format($payment['pay_amount'] / 100, 2) . ' 元',
                'pay_time' => $payment['pay_time'],
                'pay_time_text' => date('Y-m-d H:i:s', $payment['pay_time']),
                'remark' => $payment['remark']
            ];
        }

        // 构建完整的状态历史
        $history = [
            'current_status' => [
                'main_status' => $order['main_status'],
                'sub_status' => $order['sub_status'],
                'main_status_text' => $orderModel->main_status[$order['main_status']]['text'] ?? '',
                'sub_status_text' => $orderModel->sub_status[$order['sub_status']]['text'] ?? '',
                'status_desc' => $order['status_desc'],
                'update_time' => $order['status_update_time'],
                'update_time_text' => $order['status_update_time'] ? date('Y-m-d H:i:s', $order['status_update_time']) : '',
                'operator_id' => $order['status_operator_id'],
                'remark' => $order['status_remark']
            ],
            'status_history' => $processedHistory,
            'payment_records' => $processedPayments,
            'key_milestones' => [
                'created' => $order['create_time'],
                'created_text' => date('Y-m-d H:i:s', $order['create_time']),
                'last_updated' => $order['update_time'],
                'last_updated_text' => date('Y-m-d H:i:s', $order['update_time']),
                'status_updated' => $order['status_update_time'],
                'status_updated_text' => $order['status_update_time'] ? date('Y-m-d H:i:s', $order['status_update_time']) : ''
            ],
            'statistics' => [
                'total_status_changes' => count($processedHistory),
                'total_payments' => count($processedPayments),
                'total_amount_paid' => array_sum(array_column($paymentRecords, 'pay_amount'))
            ]
        ];

        $this->ajaxReturn(['status' => 1, 'data' => $history]);
    }


    /**
     * 取消订单（新版本，AJAX接口）
     */
    public function cancelOrder()
    {
        $orderId = I('post.order_id');
        $reason = I('post.reason', '');

        if (empty($orderId)) {
            $this->ajaxReturn(['status' => 0, 'info' => '参数错误']);
        }

        // 权限验证：只有管理员可以取消订单
        $adminId = session('admin_id');
        if (!$adminId) {
            \Think\Log::write('取消订单失败：管理员session不存在 order_id=' . $orderId, 'ERROR');
            $this->ajaxReturn(['status' => 0, 'info' => '只有管理员可以取消订单，请重新登录']);
        }

        // 记录调试信息
        \Think\Log::write('取消订单权限检查通过 admin_id=' . $adminId . ' order_id=' . $orderId, 'INFO');

        // 验证订单存在
        $orderModel = D('TrainingOrder');
        $order = $orderModel->where(['id' => $orderId])->find();
        if (!$order) {
            $this->ajaxReturn(['status' => 0, 'info' => '订单不存在']);
        }

        // 检查订单状态是否可以取消
        if (in_array($order['sub_status'], ['completed', 'terminated'])) {
            $this->ajaxReturn(['status' => 0, 'info' => '该订单已完成或已取消，无法再次取消']);
        }

        try {
            // 开启事务
            $orderModel->startTrans();

            // 使用新的状态管理系统：更新为服务终止
            $adminId = session('admin_id');
            \Think\Log::write('开始更新订单状态 order_id=' . $orderId . ' admin_id=' . $adminId . ' reason=' . $reason, 'INFO');

            $result = $orderModel->updateOrderStatusV2(
                $orderId,
                'service_review',
                'terminated',
                [],
                $adminId,
                '管理员取消订单：' . $reason,
                'manual',
                true // 【修复】跳过资金操作确认，管理员取消订单直接执行
            );

            // 【修复】正确处理返回值：可能是true、false、数组或字符串格式的确认状态
            \Think\Log::write('订单状态结果 order_id=' . $orderId . ' admin_id=' . $adminId . ' result=' . (is_array($result) ? json_encode($result) : $result), 'INFO');

            if ($result === false) {
                \Think\Log::write('updateOrderStatusV2返回false order_id=' . $orderId . ' admin_id=' . $adminId, 'ERROR');
                throw new \Exception('更新订单状态失败，请检查权限或联系管理员');
            } elseif (is_array($result) && isset($result['status']) && $result['status'] === 'fund_operation_confirm') {
                // 这种情况不应该发生，因为我们设置了skipPaymentValidation=true
                \Think\Log::write('意外的资金操作确认状态(数组) order_id=' . $orderId . ' result=' . json_encode($result), 'WARN');
                throw new \Exception('订单取消过程中出现意外的资金操作确认状态');
            } elseif (is_string($result) && strpos($result, 'status=fund_operation_confirm') !== false) {
                // 处理字符串格式的资金操作确认状态
                \Think\Log::write('意外的资金操作确认状态(字符串) order_id=' . $orderId . ' result=' . $result, 'WARN');
                throw new \Exception('订单取消过程中出现意外的资金操作确认状态');
            }

            // 退款和奖励回退已在状态更新时自动处理（handleStatusBusinessLogic -> terminated）
            // 无需手动调用 handleCancelRefund，避免重复执行

            // 更新用户状态为沟通中
            if (!empty($order['user_id'])) {
                D('User')->where(['id' => $order['user_id']])->save(['service_status' => 0]);

                // 同步更新相关简历状态
                if (!empty($order['user_job_id'])) {
                    D('UserJob')->where(['id' => $order['user_job_id']])->save(['job_state' => 0]);
                } else {
                    // 如果没有关联简历，更新该用户所有简历的状态
                    $userInfo = D('User')->where(['id' => $order['user_id']])->find();
                    if ($userInfo && !empty($userInfo['mobile'])) {
                        D('UserJob')->where(['phone' => $userInfo['mobile']])->save(['job_state' => 0]);
                    }
                }
            }

            $orderModel->commit();

            // 记录操作日志
            \Think\Log::write('管理员取消订单成功 order_id=' . $orderId . ' admin_id=' . session('admin_id') . ' reason=' . $reason, 'INFO');

            $this->ajaxReturn(['status' => 1, 'info' => '订单取消成功']);

        } catch (\Exception $e) {
            $orderModel->rollback();
            \Think\Log::write('取消订单失败: ' . $e->getMessage(), 'ERROR');
            $this->ajaxReturn(['status' => 0, 'info' => '取消失败：' . $e->getMessage()]);
        }
    }

    /**
     * 获取学员简历信息
     */
    public function getUserJobInfo()
    {
        $userJobId = I('get.user_job_id');
        if (!$userJobId) {
            $this->ajaxReturn(['status' => 0, 'msg' => '参数错误']);
        }

        // 获取学员简历信息
        $userJob = D('UserJob')->where(['id' => $userJobId])->field('id, name, education_level, major')->find();
        if (!$userJob) {
            $this->ajaxReturn(['status' => 0, 'msg' => '学员简历不存在']);
        }

        $this->ajaxReturn(['status' => 1, 'data' => $userJob]);
    }

    /**
     * 获取表单数据
     */
    public function getFormData()
    {
        // 从简历获取学员列表
        $whereCondition = [
            'job_state' => 0 // 只显示沟通中的简历
        ];

        $userJobList = D('UserJob')->where($whereCondition)
            ->field('id, name as realname, phone as mobile, job_state, resume_type, service_station_id')
            ->select();

        // 获取简历对应的文档状态，只显示已分析完成的简历
        $userJobIds = array_column($userJobList, 'id');
        $userJobDocList = [];

        if (!empty($userJobIds)) {
            // 使用与list-joblist.html中相同的条件：is_html == 3
            $userJobDocList = D('UserJobDoc')->where([
                'user_job_id' => ['in', $userJobIds],
                'is_html' => 3 // 只获取已分析完成的简历
            ])->getField('user_job_id, id, is_html', true);
        }

        // 过滤出已分析完成的简历对应的学员
        $filteredUsers = [];
        foreach ($userJobList as $user) {
            if (isset($userJobDocList[$user['id']])) {
                // 添加简历类型文本
                if (isset($user['resume_type'])) {
                    $user['resume_type_text'] = $user['resume_type'] == 1 ? '自有简历' : '招就办简历';
                } else {
                    // 默认为自有简历
                    $user['resume_type'] = 1;
                    $user['resume_type_text'] = '自有简历';
                }

                // 确保service_station_id字段存在
                if (!isset($user['service_station_id'])) {
                    $user['service_station_id'] = '';
                }

                $filteredUsers[] = $user;
            }
        }

        // 获取项目列表 - 只获取有上架岗位的项目
        $projectList = D('Project')
            ->alias('p')
            ->join('LEFT JOIN __PROJECT_POST__ pp ON p.id = pp.project_id')
            ->where([
                'p.status' => 1,
                'pp.status' => 1
            ])
            ->group('p.id')
            ->field('p.id, p.name')
            ->select();

        $returnData = [
            'status' => 1,
            'data' => [
                'users' => $filteredUsers,
                'projects' => $projectList
            ]
        ];

        $this->ajaxReturn($returnData);
    }

    /**
     * 获取项目岗位
     */
    public function getProjectPosts()
    {
        $projectId = I('get.project_id');
        if (!$projectId) {
            $this->ajaxReturn(['status' => 0, 'msg' => '参数错误']);
        }

        // 获取项目下的岗位列表
        $posts = D('ProjectPost')->where([
            'project_id' => $projectId,
            'status' => 1
        ])->field('id, job_name, service_price, max_price, is_free')->select();

        // 处理价格信息
        foreach ($posts as &$post) {
            if ($post['is_free'] == 1) {
                $post['price'] = 0;
                $post['price_text'] = '公益实习';
            } else {
                $post['price'] = $post['service_price'];
                $post['price_text'] = '本科及以上学历';
            }
        }

        $this->ajaxReturn(['status' => 1, 'data' => $posts]);
    }

    /**
     * 创建培训订单
     */
    public function create()
    {
        if (IS_POST) {
            $orderModel = D("TrainingOrder");

            // 获取岗位信息
            $postId = I('post.post_id');
            $post = D('ProjectPost')->where(['id' => $postId, 'status' => 1])->find();
            if (!$post) {
                if (IS_AJAX) {
                    $this->ajaxReturn(['status' => 0, 'info' => '培训岗位不存在或已下架']);
                } else {
                    $this->error('培训岗位不存在或已下架');
                }
                return;
            }

            // 获取简历信息
            $userJobId = I('post.user_id');
            $userJob = D('UserJob')->where(['id' => $userJobId])->find();
            if (!$userJob) {
                if (IS_AJAX) {
                    $this->ajaxReturn(['status' => 0, 'info' => '学员简历不存在']);
                } else {
                    $this->error('学员简历不存在');
                }
                return;
            }

            // 检查简历状态，服务终止状态的简历无法报名培训
            if ($userJob['job_state'] == 3) {
                if (IS_AJAX) {
                    $this->ajaxReturn(['status' => 0, 'info' => '该简历已标记为服务终止状态，无法报名培训']);
                } else {
                    $this->error('该简历已标记为服务终止状态，无法报名培训');
                }
                return;
            }

            // 检查简历是否已分析完成
            $userJobDoc = D('UserJobDoc')->where(['user_job_id' => $userJobId, 'is_html' => 3])->find();
            if (!$userJobDoc) {
                if (IS_AJAX) {
                    $this->ajaxReturn(['status' => 0, 'info' => '该简历尚未分析完成，无法报名']);
                } else {
                    $this->error('该简历尚未分析完成，无法报名');
                }
                return;
            }

            // 检查是否已经报名同一岗位
            $existOrder = $orderModel->where([
                'user_job_id' => $userJobId,
                'post_id' => $postId,
                'sub_status' => ['not in', ['terminated']] // 允许取消报名后重新报名
            ])->find();

            if ($existOrder) {
                if (IS_AJAX) {
                    $this->ajaxReturn(['status' => 0, 'info' => '该学员已报名此岗位培训']);
                } else {
                    $this->error('该学员已报名此岗位培训');
                }
                return;
            }

            // 获取或创建用户
            $user = D('User')->where(['mobile' => $userJob['phone']])->find();
            if (!$user) {
                // 自动创建用户
                $userData = [
                    'mobile' => $userJob['phone'],
                    'nickname' => $userJob['name'],
                    'service_id' => 2,
                    'service_status' => 0,
                    'service_station_id' => $userJob['service_station_id'],
                    'create_time' => time()
                ];
                $userId = D('User')->add($userData);
                if (!$userId) {
                    if (IS_AJAX) {
                        $this->ajaxReturn(['status' => 0, 'info' => '创建用户失败']);
                    } else {
                        $this->error('创建用户失败');
                    }
                    return;
                }
            } else {
                $userId = $user['id'];
            }

            // 准备订单数据
            $feeAmount = I('post.fee_amount');
            if (!$feeAmount || $feeAmount <= 0) {
                if (IS_AJAX) {
                    $this->ajaxReturn(['status' => 0, 'info' => '请输入有效的报名费']);
                } else {
                    $this->error('请输入有效的报名费');
                }
                return;
            }

            // 检查是否为招就办简历，如果是则验证价格配置
            $isZsbResume = ($userJob['resume_type'] == 2);
            $zsbId = null;

            if ($isZsbResume) {
                $zsbId = $userJob['service_station_id'];

                // 验证招就办价格配置
                $zsbPriceConfig = D('ZsbPostPrice')->where([
                    'zsb_id' => $zsbId,
                    'post_id' => $postId,
                    'status' => 1
                ])->find();

                if (!$zsbPriceConfig) {
                    if (IS_AJAX) {
                        $this->ajaxReturn(['status' => 0, 'info' => '该岗位未配置招就办价格']);
                    } else {
                        $this->error('该岗位未配置招就办价格');
                    }
                    return;
                }

                // 验证提交的价格是否与配置的价格一致
                $expectedFeeAmount = round($zsbPriceConfig['sale_price'] / 100, 2);
                if (abs($feeAmount - $expectedFeeAmount) > 0.01) {
                    if (IS_AJAX) {
                        $this->ajaxReturn(['status' => 0, 'info' => '报名费与招就办配置价格不符']);
                    } else {
                        $this->error('报名费与招就办配置价格不符');
                    }
                    return;
                }
            }

            $data = [
                'user_id' => $userId,
                'user_job_id' => $userJobId,
                'post_id' => $postId,
                'project_id' => $post['project_id'],
                'fee_amount' => $feeAmount * 100, // 转换为分
                'station_id' => $userJob['service_station_id'],
                'create_time' => time()
            ];

            // 如果是招就办简历，添加招就办相关信息
            if ($isZsbResume && $zsbId) {
                $data['zsb_id'] = $zsbId;
                $data['is_zsb_order'] = 1;
            }

            // 创建订单
            $orderId = $orderModel->createOrder($data);

            if ($orderId) {
                if (IS_AJAX) {
                    $this->ajaxReturn(['status' => 1, 'info' => '报名成功', 'url' => U('training/detail', ['id' => $orderId])]);
                } else {
                    $this->success('报名成功', U('training/detail', ['id' => $orderId]));
                }
            } else {
                if (IS_AJAX) {
                    $this->ajaxReturn(['status' => 0, 'info' => '报名失败']);
                } else {
                    $this->error('报名失败');
                }
            }
        } else {
            $this->error('请求方式错误');
        }
    }

    /**
     * 获取招就办岗位价格配置
     */
    public function getZsbPostPrice()
    {
        $postId = I('get.post_id', 0, 'intval');
        $zsbId = I('get.zsb_id', 0, 'intval');

        if (!$postId) {
            $this->ajaxReturn(['status' => 0, 'msg' => '岗位ID不能为空']);
        }

        if (!$zsbId) {
            $this->ajaxReturn(['status' => 0, 'msg' => '招就办ID不能为空']);
        }

        try {
            // 获取招就办价格配置
            $zsbPriceModel = D('ZsbPostPrice');
            $priceConfig = $zsbPriceModel->where([
                'zsb_id' => $zsbId,
                'post_id' => $postId,
                'status' => 1
            ])->find();

            if (!$priceConfig) {
                $this->ajaxReturn(['status' => 0, 'msg' => '该岗位未配置价格']);
            }

            // 获取岗位信息
            $post = D('ProjectPost')->where(['id' => $postId])->find();
            if (!$post) {
                $this->ajaxReturn(['status' => 0, 'msg' => '岗位不存在']);
            }

            // 格式化价格数据（从分转换为元）
            $data = [
                'sale_price' => $priceConfig['sale_price'], // 分单位
                'sale_price_formatted' => round($priceConfig['sale_price'] / 100, 2), // 元单位
                'cost_price' => $priceConfig['cost_price'], // 分单位
                'cost_price_formatted' => round($priceConfig['cost_price'] / 100, 2), // 元单位
                'commission' => $priceConfig['commission'], // 分单位
                'commission_formatted' => round($priceConfig['commission'] / 100, 2), // 元单位
                'post_name' => $post['job_name']
            ];

            $this->ajaxReturn(['status' => 1, 'data' => $data]);

        } catch (\Exception $e) {
            \Think\Log::write('获取招就办价格配置失败：' . $e->getMessage(), 'ERROR');
            $this->ajaxReturn(['status' => 0, 'msg' => '获取价格配置失败']);
        }
    }

}